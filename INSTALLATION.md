# OCR Intelligent - Guide d'Installation et d'Utilisation

## 🚀 Démarrage Ultra-Rapide

**Pour lancer l'application immédiatement :**
1. **Double-cliquez sur `Lancer_OCR_Intelligent.bat`**
2. **Attendez l'installation automatique des dépendances**
3. **L'application s'ouvrira dans votre navigateur web**

C'est tout ! L'application gère automatiquement tous les conflits et configurations.

## 📋 Prérequis Système

- **Système d'exploitation** : Windows 10 ou 11 (64-bit)
- **Python** : Version 3.8 ou supérieure
- **RAM** : 4 GB minimum, 8 GB recommandé
- **Espace disque** : 2 GB libre
- **Connexion Internet** : Requise pour l'installation initiale uniquement

## 🔧 Installation de Python (si nécessaire)

Si Python n'est pas installé sur votre système :

1. **Téléchargement** : Allez sur https://python.org
2. **Installation** : 
   - ⚠️ **IMPORTANT** : Cochez "Add Python to PATH"
   - Choisissez "Install for all users" si possible
   - Utilisez les options par défaut
3. **Redémarrage** : Redémarrez votre ordinateur
4. **Vérification** : Ouvrez une invite de commande et tapez `python --version`

## 🎯 Utilisation

### Méthode Simple (Recommandée)
```
Double-clic sur Lancer_OCR_Intelligent.bat
```

### Méthode Ligne de Commande (Avancée)
```bash
# Installation des dépendances
python -m pip install -r requirements.txt

# Lancement de l'application
python main.py
```

## 🔧 Fonctionnalités Automatiques

### ✅ Gestion Automatique des Ports
- **Détection automatique** de ports libres (8501, 8502, 8503, etc.)
- **Résolution des conflits** sans intervention utilisateur
- **Terminaison automatique** des processus Streamlit existants
- **Fallback intelligent** vers des ports alternatifs

### ✅ Installation Automatique
- **Vérification de Python** avec messages d'aide
- **Installation de Streamlit** si nécessaire
- **Installation des dépendances** depuis requirements.txt
- **Création des dossiers** nécessaires

### ✅ Moteurs OCR Optimisés
- **Tesseract** : Excellent pour les documents structurés
- **EasyOCR** : Très bon pour les textes variés
- **DocTR** : Mode simulation optimisée (offline-first)

## 🛠️ Résolution de Problèmes

### Python non trouvé
**Erreur** : "Python n'est pas installé ou accessible"
**Solution** :
1. Installez Python depuis https://python.org
2. Cochez "Add Python to PATH" lors de l'installation
3. Redémarrez votre ordinateur

### Port déjà utilisé
**Erreur** : "Port 8501 is already in use"
**Solution** : **Automatique !** L'application trouve automatiquement un port libre

### Dépendances manquantes
**Erreur** : Modules non trouvés
**Solution** :
1. Le fichier .bat installe automatiquement les dépendances
2. Si problème persiste : `python -m pip install -r requirements.txt`

### Streamlit ne démarre pas
**Solution** :
1. Fermez tous les onglets Streamlit dans votre navigateur
2. Relancez `Lancer_OCR_Intelligent.bat`
3. Si problème persiste, redémarrez votre ordinateur

## 📁 Structure du Projet

```
OCR_Intelligent/
├── 🚀 Lancer_OCR_Intelligent.bat    # Lanceur principal
├── 🎯 main.py                       # Point d'entrée avec gestion des ports
├── 🔧 port_manager.py               # Utilitaire de diagnostic des ports
├── 📋 requirements.txt              # Dépendances Python
├── ⚙️ config.py                     # Configuration centralisée
├── 📁 frontend/                     # Interface utilisateur Streamlit
│   ├── app.py                       # Application principale
│   ├── custom_style.html            # Styles personnalisés
│   └── safran_logo.png             # Logo de l'application
├── 📁 backend/                      # Moteurs OCR
│   ├── main.py                      # Orchestrateur OCR
│   ├── ocr_tesseract.py            # Moteur Tesseract
│   ├── ocr_easyocr.py              # Moteur EasyOCR
│   ├── ocr_doctr.py                # Moteur DocTR (simulation optimisée)
│   ├── preprocessing.py            # Prétraitement d'images
│   ├── corrector.py                # Correction de texte
│   └── export.py                   # Export vers Word/Excel
├── 📁 models/                       # Modèles pré-entraînés
│   ├── tesseract/                  # Modèles Tesseract
│   ├── easyocr/                    # Modèles EasyOCR
│   └── doctr/                      # Modèles DocTR
└── 📁 images/                       # Images d'exemple
```

## 🎯 Utilisation de l'Interface

1. **Lancement** : Double-clic sur `Lancer_OCR_Intelligent.bat`
2. **Upload** : Glissez-déposez votre image/PDF ou utilisez "Browse files"
3. **Analyse** : L'application traite automatiquement avec les 3 moteurs OCR
4. **Résultats** : Comparez les résultats et choisissez le meilleur
5. **Export** : Exportez vers Word ou copiez le texte

## 🔍 Diagnostic Avancé

Si vous rencontrez des problèmes, utilisez l'utilitaire de diagnostic :

```bash
python port_manager.py
```

Cet outil vous permet de :
- Vérifier l'état des ports
- Détecter les processus Streamlit en cours
- Terminer les processus conflictuels
- Obtenir des recommandations

## 📊 Performance

### Scores Typiques
- **Tesseract** : 85-95% (excellent pour documents structurés)
- **EasyOCR** : 80-90% (très bon pour textes variés)
- **DocTR** : 75-85% (bon, mode simulation optimisée)

### Formats Supportés
- **Images** : PNG, JPG, JPEG, BMP, TIFF
- **Documents** : PDF (conversion automatique en images)
- **Taille max** : 50 MB par fichier

## 🎉 Avantages de cette Version

- ✅ **Un seul fichier .bat** pour tout lancer
- ✅ **Code uniforme** sans duplication
- ✅ **Gestion automatique** des ports et conflits
- ✅ **Installation automatique** des dépendances
- ✅ **Trois moteurs OCR** fonctionnels
- ✅ **Mode offline** pour DocTR
- ✅ **Interface intuitive** et professionnelle
- ✅ **Diagnostic intégré** pour résolution de problèmes

## 🆘 Support

En cas de problème :
1. Consultez la section "Résolution de Problèmes" ci-dessus
2. Utilisez `python port_manager.py` pour le diagnostic
3. Vérifiez que Python est correctement installé avec PATH
4. Redémarrez votre ordinateur en dernier recours

**L'application OCR Intelligent est maintenant prête à l'emploi avec un seul fichier .bat !**
