"""
Interface Streamlit pour l'application OCR Intelligent
"""
import os
import sys
import glob
import shutil
from pathlib import Path

# Configuration de l'environnement
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"

import warnings
warnings.filterwarnings("ignore")

import streamlit as st
import fitz
from PIL import Image
from streamlit.components.v1 import html
import docx

# Configuration du chemin backend
current_dir = Path(__file__).parent
backend_dir = current_dir.parent
sys.path.insert(0, str(backend_dir))

from backend.main import run_all_ocr_methods
from backend.export import export_to_word


def clear_output_directory():
    """
    Nettoie le dossier output à chaque relancement de l'application
    Supprime tous les fichiers .docx générés précédemment
    """
    output_dir = Path("output")

    if output_dir.exists():
        try:
            # Compter les fichiers avant nettoyage
            files_before = list(output_dir.glob("*.docx"))
            files_count = len(files_before)

            if files_count > 0:
                # Supprimer tous les fichiers .docx
                cleaned_count = 0
                for file_path in files_before:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        st.warning(f"Impossible de supprimer {file_path}: {e}")

                if cleaned_count > 0:
                    st.info(f"Dossier output nettoyé: {cleaned_count} fichier(s) supprimé(s)")
            else:
                st.info("Dossier output déjà propre")

        except Exception as e:
            st.error(f"Erreur lors du nettoyage du dossier output: {e}")
    else:
        # Créer le dossier s'il n'existe pas
        output_dir.mkdir(exist_ok=True)
        st.info("Dossier output créé")


# [CLEAN] Nettoyage automatique du dossier output au démarrage
if 'output_cleaned' not in st.session_state:
    clear_output_directory()
    st.session_state.output_cleaned = True

st.set_page_config(page_title="OCR Intelligent", layout="wide")
st.image("frontend/safran_logo.png", width=250)
with open("frontend/custom_style.html") as f:
    html(f.read(), height=0)
st.markdown("<h1> OCR Intelligent </h1>", unsafe_allow_html=True)

uploaded_file = st.file_uploader(" Téléversez une image ou un PDF", type=["png", "jpg", "jpeg", "pdf"])

if uploaded_file:
    os.makedirs("images", exist_ok=True)
    os.makedirs("output", exist_ok=True)
    file_path = os.path.join("images", uploaded_file.name)

    if uploaded_file.type == "application/pdf":
        doc = fitz.open(stream=uploaded_file.read(), filetype="pdf")
        st.warning(f"PDF détecté, traitement de {len(doc)} pages.")
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            image_path = os.path.join("images", f"page_{i+1}.png")
            pix.save(image_path)
            image_paths.append(image_path)
    else:
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        image_paths = [file_path]

    # --- OCR multiple
    with st.spinner(" Analyse en cours, merci de patienter..."):
        results, best_method, word_file = run_all_ocr_methods(image_paths)
    st.success(" Analyse terminée !")

    # --- Affichage
    col_img, col_text = st.columns([1, 3])
    with col_img:
        st.image(image_paths[0], caption=" Image analysée")

    with col_text:
        # Réorganiser les méthodes pour mettre la fusion en premier
        ordered_methods = []
        if "fusion" in results:
            ordered_methods.append("fusion")

        # Ajouter les autres méthodes
        for method in results:
            if method != "fusion":
                ordered_methods.append(method)

        # Créer les colonnes pour chaque méthode
        cols = st.columns(len(results))

        # Afficher les résultats dans l'ordre défini
        for i, method in enumerate(ordered_methods):
            data = results[method]
            with cols[i]:
                # Affichage amélioré avec indicateur visuel de fiabilité
                conf_value = data['avg_conf']

                # Déterminer la couleur selon le niveau de confiance
                if conf_value >= 85:
                    conf_color = "green"
                    conf_emoji = "🟢"
                    conf_label = "Excellente"
                elif conf_value >= 70:
                    conf_color = "limegreen"
                    conf_emoji = "🟢"
                    conf_label = "Bonne"
                elif conf_value >= 50:
                    conf_color = "orange"
                    conf_emoji = "🟠"
                    conf_label = "Moyenne"
                else:
                    conf_color = "red"
                    conf_emoji = "🔴"
                    conf_label = "Faible"

                # Style spécial pour la fusion
                is_fusion = method == "fusion"
                title_style = "font-weight: bold; background: linear-gradient(90deg, #4b6cb7 0%, #182848 100%); color: white; padding: 8px; border-radius: 5px;" if is_fusion else ""

                # Titre avec méthode et fiabilité
                method_display = "FUSION INTELLIGENTE" if is_fusion else method.upper()
                st.markdown(f"<div style='{title_style}'><h3 style='margin: 0; {'color: white;' if is_fusion else ''}'>{method_display}</h3></div>", unsafe_allow_html=True)

                # Badge de fiabilité
                st.markdown(
                    f"<div style='display: flex; align-items: center; margin-top: 10px;'>"
                    f"<div style='font-weight: bold; margin-right: 10px;'>Fiabilité:</div>"
                    f"<div style='color: {conf_color}; font-weight: bold;'>{conf_emoji} {conf_value:.1f}% ({conf_label})</div>"
                    f"</div>",
                    unsafe_allow_html=True
                )

                # Zone de texte avec le résultat
                text_height = 300 if is_fusion else 250
                text_label = "Texte fusionné (combinaison optimale)" if is_fusion else "Texte extrait"
                st.text_area(text_label, "\n".join(data['lines']), height=text_height, key=f"text_{method}")

                # Afficher des statistiques supplémentaires
                with st.expander("Détails" + (" de la fusion" if is_fusion else "")):
                    st.markdown(f"**Lignes détectées:** {len(data['lines'])}")
                    st.markdown(f"**Caractères:** {sum(len(line) for line in data['lines'])}")

                    if is_fusion:
                        st.markdown("**Source:** Fusion caractère par caractère des 3 modèles")
                        st.markdown("**Algorithme:** Vote pondéré par confiance")

                    # Histogramme des scores de confiance par ligne
                    if len(data['confs']) > 0:
                        import pandas as pd
                        import numpy as np

                        # Créer un histogramme simplifié des scores
                        conf_ranges = ["0-25%", "25-50%", "50-75%", "75-100%"]
                        conf_counts = [0, 0, 0, 0]

                        for conf in data['confs']:
                            if conf < 25:
                                conf_counts[0] += 1
                            elif conf < 50:
                                conf_counts[1] += 1
                            elif conf < 75:
                                conf_counts[2] += 1
                            else:
                                conf_counts[3] += 1

                        conf_df = pd.DataFrame({
                            "Plage": conf_ranges,
                            "Nombre": conf_counts
                        })

                        st.markdown("**Distribution des scores:**")
                        st.bar_chart(conf_df.set_index("Plage"))

        # Section d'analyse comparative
        st.markdown("---")
        if "fusion" in results:
            st.markdown("### 🔬 Analyse Comparative des Méthodes")

            # Créer un tableau comparatif
            import pandas as pd

            comparison_data = []
            for method, data in results.items():
                comparison_data.append({
                    "Méthode": method.upper(),
                    "Fiabilité (%)": f"{data['avg_conf']:.1f}%",
                    "Lignes": len(data['lines']),
                    "Caractères": sum(len(line) for line in data['lines']),
                    "Score Global": data['avg_conf'] * len(data['lines']) / 100
                })

            df_comparison = pd.DataFrame(comparison_data)
            st.dataframe(df_comparison, use_container_width=True)

            # Graphique de comparaison
            st.markdown("#### 📊 Comparaison Visuelle")
            chart_data = pd.DataFrame({
                'Méthode': [d['Méthode'] for d in comparison_data],
                'Fiabilité': [float(d['Fiabilité (%)'].replace('%', '')) for d in comparison_data]
            })
            st.bar_chart(chart_data.set_index('Méthode'))

            # Explication de la fusion
            with st.expander("🧠 Comment fonctionne la Fusion Intelligente"):
                st.markdown("""
                **Algorithme de Fusion Multi-Modèles:**

                1. **Alignement des lignes** : Les lignes des 3 modèles sont alignées par similarité
                2. **Fusion caractère par caractère** : Pour chaque position, le meilleur caractère est choisi
                3. **Vote pondéré** : Chaque modèle vote avec un poids basé sur sa confiance
                4. **Gestion des confusions** : L'algorithme connaît les erreurs typiques OCR (I/l/1, O/0, etc.)
                5. **Optimisation contextuelle** : Bonus pour les mots-clés et structures de documents

                **Avantages:**
                - ✅ Combine les forces de chaque modèle
                - ✅ Réduit les erreurs individuelles
                - ✅ Améliore la précision globale
                - ✅ Adaptatif selon le type de contenu
                """)

    # Affichage du résultat final avec message spécial pour la fusion
    if best_method == "fusion":
        st.success("🎉 La fusion intelligente a produit le meilleur résultat !")
        st.info("Ce texte combine les meilleures parties de Tesseract, EasyOCR et DocTR")
    else:
        st.success(f"🏆 Meilleure méthode : {best_method.upper()}")
    with open(word_file, "rb") as f:
        st.download_button(" Télécharger le Word", f, file_name=os.path.basename(word_file))

# --- Upload d'un Word corrigé ---
st.markdown("---")
st.markdown("### Uploader un Word corrigé")
corrected_file = st.file_uploader("Déposez ici votre Word corrigé", type=["docx"], key="corrected")
if corrected_file is not None:
    os.makedirs("corrected", exist_ok=True)
    # Extraire le texte du Word corrigé
    doc = docx.Document(corrected_file)
    corrected_text = "\n".join([p.text for p in doc.paragraphs])
    
    # Sauvegarder la correction
    base_name = uploaded_file.name if uploaded_file else "unknown"
    txt_name = os.path.splitext(base_name)[0] + "_corrige.txt"
    corrected_path = os.path.join("corrected", txt_name)
    os.makedirs("corrected", exist_ok=True)

    with open(corrected_path, "w", encoding="utf-8") as f:
        f.write(corrected_text)
    st.success(f"Texte corrigé enregistré dans {corrected_path}")
