@echo off
title OCR Intelligent - Lanceur Simple

echo ================================================================
echo OCR INTELLIGENT - LANCEUR SIMPLE
echo ================================================================
echo.

:: Verification de Python
echo Verification de Python...
python --version
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou accessible
    echo.
    echo Installez Python depuis https://python.org
    echo Cochez "Add Python to PATH" lors de l'installation
    echo.
    pause
    exit /b 1
)

echo Python OK
echo.

:: Verification de Streamlit
echo Verification de Streamlit...
python -c "import streamlit" 2>nul
if errorlevel 1 (
    echo Installation de Streamlit...
    python -m pip install streamlit --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer Streamlit
        pause
        exit /b 1
    )
)

echo Streamlit OK
echo.

:: Creation des dossiers
if not exist output mkdir output
if not exist logs mkdir logs
if not exist corrected mkdir corrected

:: Lancement
echo ================================================================
echo LANCEMENT DE L'APPLICATION
echo ================================================================
echo.
echo L'application va demarrer...
echo Le navigateur s'ouvrira automatiquement
echo Appuyez sur Ctrl+C pour arreter
echo.

python main.py

echo.
echo ================================================================
echo APPLICATION FERMEE
echo ================================================================
pause
