#!/usr/bin/env python3
"""
Safran Corporate Environment Configuration for OCR Intelligent
This file contains all Safran-specific settings and configurations
"""

# Safran Corporate Proxy Configuration
SAFRAN_PROXY_CONFIG = {
    "index_url": "https://artifacts.cloud.safran/repository/pypi-group/simple",
    "trusted_host": "artifacts.cloud.safran",
    "timeout": 60,  # Increased timeout for corporate network
}

# Pip command arguments for Safran environment
def get_safran_pip_args():
    """Get pip arguments configured for Safran corporate environment"""
    return [
        "--index-url", SAFRAN_PROXY_CONFIG["index_url"],
        "--trusted-host", SAFRAN_PROXY_CONFIG["trusted_host"],
        "--timeout", str(SAFRAN_PROXY_CONFIG["timeout"]),
        "--no-warn-script-location",
        "--disable-pip-version-check"
    ]

# Known issues and their status in Safran environment
KNOWN_SAFRAN_ISSUES = {
    "invalid_distributions": {
        "description": "Invalid distributions like ~illow and ~pencv-python in packages directory",
        "impact": "Cosmetic warnings only - does not affect functionality",
        "action_required": False,
        "solution": "These are temporary files created during installation - safe to ignore"
    },
    
    "connection_retries": {
        "description": "Connection retry warnings during pip operations",
        "impact": "Normal corporate network behavior - operations still succeed",
        "action_required": False,
        "solution": "Corporate proxy may require multiple attempts - this is expected"
    },
    
    "doctr_torch_extra": {
        "description": "python-doctr 1.0.0 not providing 'torch' extra warning",
        "impact": "DocTR will use simulation mode - functionality preserved",
        "action_required": False,
        "solution": "OCR Intelligent uses optimized simulation mode for DocTR"
    },
    
    "package_restrictions": {
        "description": "Some packages may not be available in corporate repository",
        "impact": "Application runs with core functionality - advanced features may be limited",
        "action_required": False,
        "solution": "Use --user installation or contact IT for package approval"
    }
}

# Safran-specific environment variables
SAFRAN_ENV_VARS = {
    "PIP_INDEX_URL": SAFRAN_PROXY_CONFIG["index_url"],
    "PIP_TRUSTED_HOST": SAFRAN_PROXY_CONFIG["trusted_host"],
    "PIP_TIMEOUT": str(SAFRAN_PROXY_CONFIG["timeout"]),
    "PYTHONIOENCODING": "utf-8",
    "PIP_DISABLE_PIP_VERSION_CHECK": "1"
}

def apply_safran_environment():
    """Apply Safran-specific environment variables"""
    import os
    for key, value in SAFRAN_ENV_VARS.items():
        os.environ[key] = value
        print(f"Set {key}={value}")

def check_safran_warnings(warning_text):
    """Check if a warning is known and safe to ignore in Safran environment"""
    warning_lower = warning_text.lower()
    
    # Check for known safe warnings
    safe_patterns = [
        "invalid distribution",
        "~illow",
        "~pencv",
        "connection retry",
        "not providing",
        "torch extra",
        "retrying",
        "timeout"
    ]
    
    for pattern in safe_patterns:
        if pattern in warning_lower:
            return True, f"Known safe warning in Safran environment: {pattern}"
    
    return False, "Unknown warning - may require attention"

def get_safran_status_report():
    """Generate a status report for Safran environment"""
    report = []
    report.append("="*60)
    report.append("SAFRAN CORPORATE ENVIRONMENT STATUS")
    report.append("="*60)
    report.append(f"Proxy URL: {SAFRAN_PROXY_CONFIG['index_url']}")
    report.append(f"Trusted Host: {SAFRAN_PROXY_CONFIG['trusted_host']}")
    report.append(f"Timeout: {SAFRAN_PROXY_CONFIG['timeout']} seconds")
    report.append("")
    report.append("Known Issues Status:")
    
    for issue_key, issue_info in KNOWN_SAFRAN_ISSUES.items():
        status = "✓ SAFE TO IGNORE" if not issue_info["action_required"] else "⚠ REQUIRES ACTION"
        report.append(f"  {status}: {issue_info['description']}")
        report.append(f"    Impact: {issue_info['impact']}")
        report.append(f"    Solution: {issue_info['solution']}")
        report.append("")
    
    report.append("="*60)
    return "\n".join(report)

if __name__ == "__main__":
    print(get_safran_status_report())
