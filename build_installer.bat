@echo off
setlocal enabledelayedexpansion

:: Ensure the window stays open on any error
if "%1"=="DEBUG" (
    echo [DEBUG] Running in debug mode
    set DEBUG_MODE=1
) else (
    set DEBUG_MODE=0
)

title OCR Intelligent Installer Builder

echo ================================================================
echo OCR INTELLIGENT INSTALLER BUILDER
echo ================================================================
echo Creating single .exe installer for distribution
echo Debug Mode: %DEBUG_MODE%
echo ================================================================
echo.

:: Add comprehensive error handling
goto :main

:error_exit
echo.
echo [ERROR] Build process failed
echo [ERROR] Check the error messages above for details
echo.
echo Common solutions:
echo 1. Install Inno Setup from https://jrsoftware.org/isdl.php
echo 2. Ensure all project files are present
echo 3. Check that OCR_Intelligent_Setup.iss exists
echo 4. Verify you have write permissions to the dist folder
echo.
echo Press any key to close...
pause >nul
exit /b 1

:main
echo [DEBUG] Starting installer build process...
echo [DEBUG] Current directory: %CD%

:: Check if Inno Setup is installed with enhanced detection
echo [STEP] Checking Inno Setup installation...
echo [DEBUG] Searching for Inno Setup in standard locations...

set "INNO_PATH="
set "INNO_FOUND=0"

:: Check 64-bit Program Files
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    set "INNO_FOUND=1"
    echo [OK] Inno Setup 6 found: C:\Program Files (x86)\Inno Setup 6\
) else (
    echo [DEBUG] Inno Setup not found in C:\Program Files (x86)\Inno Setup 6\
)

:: Check 32-bit Program Files
if %INNO_FOUND%==0 (
    if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
        set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
        set "INNO_FOUND=1"
        echo [OK] Inno Setup 6 found: C:\Program Files\Inno Setup 6\
    ) else (
        echo [DEBUG] Inno Setup not found in C:\Program Files\Inno Setup 6\
    )
)

:: Check for Inno Setup 5
if %INNO_FOUND%==0 (
    if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
        set "INNO_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
        set "INNO_FOUND=1"
        echo [OK] Inno Setup 5 found: C:\Program Files (x86)\Inno Setup 5\
    )
)

if %INNO_FOUND%==0 (
    echo [ERROR] Inno Setup is not installed
    echo.
    echo [DEBUG] Searched locations:
    echo   - C:\Program Files (x86)\Inno Setup 6\
    echo   - C:\Program Files\Inno Setup 6\
    echo   - C:\Program Files (x86)\Inno Setup 5\
    echo.
    echo To create the installer, you need to install Inno Setup:
    echo 1. Download from: https://jrsoftware.org/isdl.php
    echo 2. Install Inno Setup 6 (recommended)
    echo 3. Run this script again
    echo.
    echo Press any key to close...
    pause >nul
    exit /b 1
)

echo [DEBUG] Using Inno Setup: %INNO_PATH%

:: Check if Inno Setup script exists
echo [STEP] Checking for Inno Setup script...
echo [DEBUG] Looking for OCR_Intelligent_Setup.iss in current directory...

if not exist "OCR_Intelligent_Setup.iss" (
    echo [ERROR] File OCR_Intelligent_Setup.iss does not exist
    echo [DEBUG] Current directory contents:
    dir /b *.iss 2>nul
    if errorlevel 1 (
        echo [DEBUG] No .iss files found in current directory
    )
    echo.
    echo Make sure the Inno Setup script is present in the current directory
    echo Expected file: %CD%\OCR_Intelligent_Setup.iss
    echo.
    echo Press any key to close...
    pause >nul
    exit /b 1
)

echo [OK] Inno Setup script found: OCR_Intelligent_Setup.iss

:: Create distribution folder
echo [STEP] Preparing build environment...
if not exist "dist" mkdir dist

:: Check required files
echo [STEP] Verifying source files...
set "MISSING_FILES="

if not exist "main.py" (
    echo [WARNING] main.py missing
    set "MISSING_FILES=1"
)

if not exist "Lancer_OCR_Intelligent.bat" (
    echo [WARNING] Lancer_OCR_Intelligent.bat missing
    set "MISSING_FILES=1"
)

if not exist "requirements.txt" (
    echo [WARNING] requirements.txt missing
    set "MISSING_FILES=1"
)

if not exist "frontend\app.py" (
    echo [WARNING] frontend\app.py missing
    set "MISSING_FILES=1"
)

if not exist "backend\main.py" (
    echo [WARNING] backend\main.py missing
    set "MISSING_FILES=1"
)

if defined MISSING_FILES (
    echo [WARNING] Some files are missing
    echo The installer will be created but may not work correctly
    echo.
    choice /C YN /M "Do you want to continue anyway"
    if errorlevel 2 exit /b 1
)

:: Build the installer
echo.
echo [STEP] Building installer...
echo This may take a few minutes...
echo.

"%INNO_PATH%" "OCR_Intelligent_Setup.iss"

if errorlevel 1 (
    echo.
    echo [ERROR] Failed to build installer
    echo Check the errors above
    pause
    exit /b 1
)

:: Check if installer was created
if exist "dist\OCR_Intelligent_Setup_v2.0.0.exe" (
    echo.
    echo ================================================================
    echo BUILD SUCCESSFUL!
    echo ================================================================
    echo Installer created successfully:
    echo File: dist\OCR_Intelligent_Setup_v2.0.0.exe
    echo.
    echo File size:
    for %%F in ("dist\OCR_Intelligent_Setup_v2.0.0.exe") do echo %%~zF bytes
    echo.
    echo This unique .exe installer contains:
    echo - All application files
    echo - Automatic Python verification
    echo - Dependency installation
    echo - Shortcut creation
    echo - Integrated uninstaller
    echo.
    echo You can now distribute this single .exe file!
    echo.
    choice /C YN /M "Do you want to open the destination folder"
    if not errorlevel 2 explorer "dist"
) else (
    echo.
    echo [ERROR] Installer not found in dist folder
    echo Check compilation errors
)

echo.
echo ================================================================
echo BUILD COMPLETED
echo ================================================================
echo.
echo Build process finished. Check the messages above for results.
echo.
echo If the build was successful:
echo - The installer is in the dist\ folder
echo - You can distribute the .exe file
echo.
echo If the build failed:
echo - Check the error messages above
echo - Ensure all required files are present
echo - Verify Inno Setup is properly installed
echo.
echo Press any key to close this window...
pause >nul

:: End of script
goto :eof
