@echo off
title Test Construction Installateur

echo ================================================================
echo TEST DE CONSTRUCTION D'INSTALLATEUR
echo ================================================================
echo.

:: Test 1: Inno Setup
echo [TEST 1] Verification d'Inno Setup...
set "INNO_FOUND=0"

if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    echo SUCCES: Inno Setup 6 trouve (x86)
    set "INNO_FOUND=1"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    echo SUCCES: Inno Setup 6 trouve (x64)
    set "INNO_FOUND=1"
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    echo SUCCES: Inno Setup 5 trouve
    set "INNO_FOUND=1"
) else (
    echo ECHEC: Inno Setup non trouve
)
echo.

:: Test 2: Script Inno Setup
echo [TEST 2] Verification du script...
if exist "OCR_Intelligent_Setup.iss" (
    echo SUCCES: OCR_Intelligent_Setup.iss trouve
) else (
    echo ECHEC: OCR_Intelligent_Setup.iss manquant
)
echo.

:: Test 3: Fichiers du projet
echo [TEST 3] Verification des fichiers du projet...
set "FILES_OK=1"

if exist "main.py" (
    echo SUCCES: main.py
) else (
    echo ECHEC: main.py manquant
    set "FILES_OK=0"
)

if exist "Lancer_OCR_Intelligent.bat" (
    echo SUCCES: Lancer_OCR_Intelligent.bat
) else (
    echo ECHEC: Lancer_OCR_Intelligent.bat manquant
    set "FILES_OK=0"
)

if exist "frontend\app.py" (
    echo SUCCES: frontend\app.py
) else (
    echo ECHEC: frontend\app.py manquant
    set "FILES_OK=0"
)

if exist "backend\main.py" (
    echo SUCCES: backend\main.py
) else (
    echo ECHEC: backend\main.py manquant
    set "FILES_OK=0"
)
echo.

:: Resultat
echo ================================================================
echo RESULTAT DU TEST
echo ================================================================
echo.

if %INNO_FOUND%==1 (
    echo Inno Setup: DISPONIBLE
) else (
    echo Inno Setup: MANQUANT
)

if %FILES_OK%==1 (
    echo Fichiers projet: COMPLETS
) else (
    echo Fichiers projet: INCOMPLETS
)

echo.

if %INNO_FOUND%==1 (
    if %FILES_OK%==1 (
        echo VERDICT: PRET POUR CONSTRUCTION
        echo.
        echo Vous pouvez utiliser:
        echo - build_installer.bat (version complete)
        echo - Build_Simple.bat (version simplifiee)
    ) else (
        echo VERDICT: FICHIERS MANQUANTS
        echo Verifiez la structure du projet
    )
) else (
    echo VERDICT: INNO SETUP REQUIS
    echo.
    echo Installez Inno Setup depuis:
    echo https://jrsoftware.org/isdl.php
)

echo.
pause
