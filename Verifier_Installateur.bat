@echo off
title Verification Installateur OCR

echo ================================================================
echo VERIFICATION DE L'INSTALLATEUR OCR INTELLIGENT
echo ================================================================
echo.

:: Verification de l'existence de l'installateur
echo [ETAPE 1] Verification de l'installateur...
if exist "dist\OCR_Intelligent_Setup_v2.0.0.exe" (
    echo SUCCES: Installateur trouve
    
    :: Affichage des informations sur le fichier
    echo.
    echo Informations sur l'installateur:
    echo Nom: OCR_Intelligent_Setup_v2.0.0.exe
    echo Emplacement: %CD%\dist\
    
    :: <PERSON><PERSON> du fichier
    for %%F in ("dist\OCR_Intelligent_Setup_v2.0.0.exe") do (
        set /a "size_mb=%%~zF/1024/1024"
        echo Taille: %%~zF octets (~!size_mb! MB)
    )
    
    :: Date de creation
    for %%F in ("dist\OCR_Intelligent_Setup_v2.0.0.exe") do (
        echo Date de creation: %%~tF
    )
    
) else (
    echo ECHEC: Installateur non trouve
    echo L'installateur devrait etre dans: %CD%\dist\
    goto :error
)

echo.

:: Verification de l'integrite (test basique)
echo [ETAPE 2] Test d'integrite basique...
if exist "dist\OCR_Intelligent_Setup_v2.0.0.exe" (
    :: Verification que le fichier n'est pas vide
    for %%F in ("dist\OCR_Intelligent_Setup_v2.0.0.exe") do (
        if %%~zF GTR 1000000 (
            echo SUCCES: Taille du fichier correcte (^>1MB)
        ) else (
            echo ECHEC: Fichier trop petit, probablement corrompu
            goto :error
        )
    )
) else (
    echo ECHEC: Impossible de verifier l'integrite
    goto :error
)

echo.

:: Verification des composants sources
echo [ETAPE 3] Verification des composants sources...
set "COMPONENTS_OK=1"

if exist "main.py" (
    echo SUCCES: main.py present
) else (
    echo ECHEC: main.py manquant
    set "COMPONENTS_OK=0"
)

if exist "Lancer_OCR_Intelligent.bat" (
    echo SUCCES: Lancer_OCR_Intelligent.bat present
) else (
    echo ECHEC: Lancer_OCR_Intelligent.bat manquant
    set "COMPONENTS_OK=0"
)

if exist "frontend\app.py" (
    echo SUCCES: frontend\app.py present
) else (
    echo ECHEC: frontend\app.py manquant
    set "COMPONENTS_OK=0"
)

if exist "backend\main.py" (
    echo SUCCES: backend\main.py present
) else (
    echo ECHEC: backend\main.py manquant
    set "COMPONENTS_OK=0"
)

if exist "models" (
    echo SUCCES: Dossier models present
) else (
    echo ECHEC: Dossier models manquant
    set "COMPONENTS_OK=0"
)

echo.

:: Resultat final
echo ================================================================
echo RESULTAT DE LA VERIFICATION
echo ================================================================
echo.

if %COMPONENTS_OK%==1 (
    echo VERDICT: INSTALLATEUR PRET POUR DISTRIBUTION
    echo.
    echo L'installateur OCR_Intelligent_Setup_v2.0.0.exe est pret !
    echo.
    echo Vous pouvez:
    echo 1. Distribuer ce fichier .exe unique
    echo 2. L'installer sur d'autres machines Windows
    echo 3. Le tester en l'executant
    echo.
    echo L'installateur contient:
    echo - Application OCR Intelligent complete
    echo - Tous les modeles pre-entraines
    echo - Lanceur automatique
    echo - Desinstallateur integre
    echo.
    
    choice /C ON /M "Voulez-vous ouvrir le dossier dist"
    if not errorlevel 2 explorer "dist"
    
) else (
    echo VERDICT: PROBLEMES DETECTES
    echo.
    echo Certains composants sources sont manquants.
    echo L'installateur pourrait ne pas fonctionner correctement.
    echo Verifiez la structure du projet avant distribution.
)

echo.
echo Verification terminee.
pause
exit /b 0

:error
echo.
echo ================================================================
echo ERREUR DE VERIFICATION
echo ================================================================
echo.
echo L'installateur n'a pas pu etre verifie correctement.
echo Relancez la construction avec build_installer.bat
echo.
pause
exit /b 1
