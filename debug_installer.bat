@echo off
setlocal enabledelayedexpansion

title OCR Intelligent - Debug Installer Builder

echo ================================================================
echo OCR INTELLIGENT - DEBUG INSTALLER BUILDER
echo ================================================================
echo This script will help diagnose issues with the installer builder
echo ================================================================
echo.

:: Enable verbose output
set DEBUG_MODE=1

echo [DEBUG] Starting installer diagnostic checks...
echo [DEBUG] Current directory: %CD%
echo [DEBUG] Current time: %DATE% %TIME%
echo [DEBUG] User: %USERNAME%
echo [DEBUG] Computer: %COMPUTERNAME%
echo.

:: Check for Inno Setup
echo [STEP 1] Checking for Inno Setup installation...
set INNO_FOUND=0

echo [DEBUG] Checking C:\Program Files (x86)\Inno Setup 6\...
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    echo [OK] Inno Setup 6 found: C:\Program Files (x86)\Inno Setup 6\ISCC.exe
    set INNO_FOUND=1
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
)

echo [DEBUG] Checking C:\Program Files\Inno Setup 6\...
if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    echo [OK] Inno Setup 6 found: C:\Program Files\Inno Setup 6\ISCC.exe
    set INNO_FOUND=1
    set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
)

echo [DEBUG] Checking C:\Program Files (x86)\Inno Setup 5\...
if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    echo [OK] Inno Setup 5 found: C:\Program Files (x86)\Inno Setup 5\ISCC.exe
    set INNO_FOUND=1
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
)

if %INNO_FOUND%==0 (
    echo [ERROR] Inno Setup not found in any standard location
    echo.
    echo Please install Inno Setup from: https://jrsoftware.org/isdl.php
    echo.
    echo Would you like to open the download page? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" start https://jrsoftware.org/isdl.php
    goto :error_exit
) else (
    echo [DEBUG] Using Inno Setup: %INNO_PATH%
)

echo.

:: Check for required project files
echo [STEP 2] Checking for required project files...
set FILES_OK=1

echo [DEBUG] Checking for OCR_Intelligent_Setup.iss...
if exist "OCR_Intelligent_Setup.iss" (
    echo [OK] OCR_Intelligent_Setup.iss found
    echo [DEBUG] File size:
    for %%F in ("OCR_Intelligent_Setup.iss") do echo   %%~zF bytes
) else (
    echo [ERROR] OCR_Intelligent_Setup.iss not found
    set FILES_OK=0
)

echo [DEBUG] Checking for main application files...
if exist "main.py" (
    echo [OK] main.py found
) else (
    echo [ERROR] main.py not found
    set FILES_OK=0
)

if exist "Lancer_OCR_Intelligent.bat" (
    echo [OK] Lancer_OCR_Intelligent.bat found
) else (
    echo [ERROR] Lancer_OCR_Intelligent.bat not found
    set FILES_OK=0
)

if exist "requirements.txt" (
    echo [OK] requirements.txt found
) else (
    echo [WARNING] requirements.txt not found
)

if exist "frontend\app.py" (
    echo [OK] frontend\app.py found
) else (
    echo [ERROR] frontend\app.py not found
    set FILES_OK=0
)

if exist "backend\main.py" (
    echo [OK] backend\main.py found
) else (
    echo [ERROR] backend\main.py not found
    set FILES_OK=0
)

echo.

:: Check directory structure
echo [STEP 3] Checking directory structure...
echo [DEBUG] Current directory contents:
dir /b
echo.
echo [DEBUG] Subdirectories:
for /d %%i in (*) do echo   [DIR] %%i

echo.

:: Check write permissions
echo [STEP 4] Checking write permissions...
echo [DEBUG] Testing write access to current directory...
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo [OK] Write access confirmed
    del test_write.tmp >nul 2>&1
) else (
    echo [ERROR] No write access to current directory
    echo You may need to run as administrator
)

echo [DEBUG] Checking/creating dist directory...
if not exist "dist" (
    mkdir "dist" 2>nul
    if errorlevel 1 (
        echo [ERROR] Cannot create dist directory
    ) else (
        echo [OK] dist directory created
    )
) else (
    echo [OK] dist directory exists
)

echo.

:: Test Inno Setup
echo [STEP 5] Testing Inno Setup...
if %INNO_FOUND%==1 (
    echo [DEBUG] Testing Inno Setup compiler...
    "%INNO_PATH%" /? >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Inno Setup compiler test failed
    ) else (
        echo [OK] Inno Setup compiler is working
    )
)

echo.

:: Summary and next steps
echo ================================================================
echo DIAGNOSTIC SUMMARY
echo ================================================================
echo.
echo Inno Setup: %INNO_FOUND% (1=found, 0=not found)
echo Project Files: %FILES_OK% (1=complete, 0=missing files)
echo.

if %INNO_FOUND%==1 (
    if %FILES_OK%==1 (
        echo [SUCCESS] All requirements met for installer creation
        echo.
        echo Would you like to run the installer builder now? (Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo.
            echo [INFO] Running build_installer.bat in debug mode...
            call "build_installer.bat" DEBUG
        )
    ) else (
        echo [ERROR] Missing required project files
        echo Please ensure all files are present before building installer
    )
) else (
    echo [ERROR] Inno Setup not installed
    echo Please install Inno Setup before building installer
)

echo.

:error_exit
echo ================================================================
echo DEBUG SESSION COMPLETE
echo ================================================================
echo Press any key to close this window...
pause >nul
exit /b 0
