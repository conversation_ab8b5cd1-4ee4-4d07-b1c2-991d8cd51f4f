"""
Module de fusion intelligente multi-modèles OCR
Combine les résultats de Tesseract, EasyOCR et DocTR pour produire un résultat optimal
"""
import re
import logging
from typing import List, Tuple, Dict, Optional
from difflib import SequenceMatcher
from collections import Counter
import numpy as np

logger = logging.getLogger(__name__)


class IntelligentOCRFusion:
    """
    Classe pour la fusion intelligente des résultats OCR multi-modèles
    """
    
    def __init__(self):
        self.character_weights = {
            'tesseract': 1.0,
            'easyocr': 1.0,
            'doctr': 1.0
        }
        
        # Patterns de caractères souvent confondus par l'OCR
        self.confusion_patterns = {
            'I': ['l', '1', '|'],
            'l': ['I', '1', '|'],
            '1': ['I', 'l', '|'],
            'O': ['0', 'Q'],
            '0': ['O', 'Q'],
            'rn': ['m'],
            'm': ['rn'],
            'cl': ['d'],
            'vv': ['w'],
            'w': ['vv']
        }
    
    def fuse_ocr_results(self, results: Dict[str, Dict]) -> Tuple[List[str], List[float]]:
        """
        Fusionne intelligemment les résultats des 3 modèles OCR
        
        Args:
            results: Dictionnaire contenant les résultats de chaque modèle
                    Format: {'tesseract': {'lines': [...], 'confs': [...]}, ...}
        
        Returns:
            Tuple (lignes_fusionnées, scores_confiance)
        """
        logger.info("Début de la fusion intelligente multi-modèles")
        
        # Extraire les résultats de chaque modèle
        tesseract_lines = results.get('tesseract', {}).get('lines', [])
        tesseract_confs = results.get('tesseract', {}).get('confs', [])
        
        easyocr_lines = results.get('easyocr', {}).get('lines', [])
        easyocr_confs = results.get('easyocr', {}).get('confs', [])
        
        doctr_lines = results.get('doctr', {}).get('lines', [])
        doctr_confs = results.get('doctr', {}).get('confs', [])
        
        # Aligner les lignes des différents modèles
        aligned_lines = self._align_lines(
            tesseract_lines, easyocr_lines, doctr_lines
        )
        
        # Fusionner ligne par ligne
        fused_lines = []
        fused_confidences = []
        
        for i, (tess_line, easy_line, doctr_line) in enumerate(aligned_lines):
            # Récupérer les scores de confiance correspondants de manière sécurisée
            tess_conf = 0.0
            easy_conf = 0.0
            doctr_conf = 0.0

            # Trouver la confiance pour Tesseract
            if tess_line.strip() and tesseract_lines and tesseract_confs:
                try:
                    tess_idx = tesseract_lines.index(tess_line) if tess_line in tesseract_lines else min(i, len(tesseract_confs)-1)
                    tess_conf = tesseract_confs[tess_idx] if tess_idx < len(tesseract_confs) else 75.0
                except (ValueError, IndexError):
                    tess_conf = 75.0  # Confiance par défaut

            # Trouver la confiance pour EasyOCR
            if easy_line.strip() and easyocr_lines and easyocr_confs:
                try:
                    easy_idx = easyocr_lines.index(easy_line) if easy_line in easyocr_lines else min(i, len(easyocr_confs)-1)
                    easy_conf = easyocr_confs[easy_idx] if easy_idx < len(easyocr_confs) else 75.0
                except (ValueError, IndexError):
                    easy_conf = 75.0

            # Trouver la confiance pour DocTR
            if doctr_line.strip() and doctr_lines and doctr_confs:
                try:
                    doctr_idx = doctr_lines.index(doctr_line) if doctr_line in doctr_lines else min(i, len(doctr_confs)-1)
                    doctr_conf = doctr_confs[doctr_idx] if doctr_idx < len(doctr_confs) else 75.0
                except (ValueError, IndexError):
                    doctr_conf = 75.0

            # Fusionner cette ligne spécifique
            fused_line, fused_conf = self._fuse_single_line(
                [(tess_line, tess_conf, 'tesseract'),
                 (easy_line, easy_conf, 'easyocr'),
                 (doctr_line, doctr_conf, 'doctr')]
            )

            # Ajouter toutes les lignes non vides (plus permissif)
            if fused_line.strip():
                fused_lines.append(fused_line)
                fused_confidences.append(fused_conf)
        
        # Final step: Remove duplicates and ensure quality
        final_lines, final_confidences = self._remove_duplicates_and_optimize(fused_lines, fused_confidences)

        logger.info(f"Fusion terminée: {len(final_lines)} lignes fusionnées (optimisées)")
        return final_lines, final_confidences

    def _remove_duplicates_and_optimize(self, lines: List[str], confidences: List[float]) -> Tuple[List[str], List[float]]:
        """
        Remove duplicate lines and optimize the final result
        """
        if not lines:
            return [], []

        # Track seen lines to avoid duplicates
        seen_lines = set()
        final_lines = []
        final_confidences = []

        for line, conf in zip(lines, confidences):
            line_clean = line.strip().lower()

            # Skip empty lines
            if not line_clean:
                continue

            # Skip if we've seen this exact line before
            if line_clean in seen_lines:
                continue

            # Check for partial duplicates (one line contained in another)
            is_duplicate = False
            for existing_line in final_lines:
                existing_clean = existing_line.strip().lower()

                # If current line is contained in existing line, skip it
                if line_clean in existing_clean and len(line_clean) < len(existing_clean) * 0.8:
                    is_duplicate = True
                    break

                # If existing line is contained in current line, replace it
                elif existing_clean in line_clean and len(existing_clean) < len(line_clean) * 0.8:
                    # Find and replace the existing line
                    for i, existing in enumerate(final_lines):
                        if existing.strip().lower() == existing_clean:
                            final_lines[i] = line
                            final_confidences[i] = max(final_confidences[i], conf)  # Keep higher confidence
                            is_duplicate = True
                            break
                    break

            if not is_duplicate:
                final_lines.append(line)
                final_confidences.append(conf)
                seen_lines.add(line_clean)

        return final_lines, final_confidences
    
    def _align_lines(self, lines1: List[str], lines2: List[str], lines3: List[str]) -> List[Tuple[str, str, str]]:
        """
        Intelligent line alignment using semantic similarity
        Groups similar lines together for optimal fusion
        """
        # Clean and prepare lines
        clean_lines1 = [line.strip() for line in lines1 if line.strip()]
        clean_lines2 = [line.strip() for line in lines2 if line.strip()]
        clean_lines3 = [line.strip() for line in lines3 if line.strip()]

        # Create all possible line combinations
        all_lines = []
        for i, line in enumerate(clean_lines1):
            all_lines.append((line, 'tesseract', i))
        for i, line in enumerate(clean_lines2):
            all_lines.append((line, 'easyocr', i))
        for i, line in enumerate(clean_lines3):
            all_lines.append((line, 'doctr', i))

        # Group similar lines together
        aligned_groups = []
        used_indices = {'tesseract': set(), 'easyocr': set(), 'doctr': set()}

        # Process each line from the most confident source (usually tesseract)
        for i, tess_line in enumerate(clean_lines1):
            if i in used_indices['tesseract']:
                continue

            # Find best matches in other sources
            best_easy_idx = self._find_best_semantic_match(tess_line, clean_lines2, used_indices['easyocr'])
            best_doctr_idx = self._find_best_semantic_match(tess_line, clean_lines3, used_indices['doctr'])

            # Create aligned group
            easy_line = clean_lines2[best_easy_idx] if best_easy_idx is not None else ""
            doctr_line = clean_lines3[best_doctr_idx] if best_doctr_idx is not None else ""

            aligned_groups.append((tess_line, easy_line, doctr_line))

            # Mark as used
            used_indices['tesseract'].add(i)
            if best_easy_idx is not None:
                used_indices['easyocr'].add(best_easy_idx)
            if best_doctr_idx is not None:
                used_indices['doctr'].add(best_doctr_idx)

        # Add remaining unmatched lines from other sources
        for i, easy_line in enumerate(clean_lines2):
            if i not in used_indices['easyocr']:
                aligned_groups.append(("", easy_line, ""))

        for i, doctr_line in enumerate(clean_lines3):
            if i not in used_indices['doctr']:
                aligned_groups.append(("", "", doctr_line))

        return aligned_groups

    def _find_best_semantic_match(self, target_line: str, candidate_lines: List[str], used_indices: set) -> Optional[int]:
        """
        Find the best semantic match for a target line
        """
        if not target_line.strip():
            return None

        best_score = 0.0
        best_index = None
        target_lower = target_line.lower()

        for i, candidate in enumerate(candidate_lines):
            if i in used_indices or not candidate.strip():
                continue

            candidate_lower = candidate.lower()

            # Calculate similarity score
            # 1. Exact match gets highest score
            if target_lower == candidate_lower:
                return i

            # 2. Check if one line contains the other
            if target_lower in candidate_lower or candidate_lower in target_lower:
                score = 0.9
            else:
                # 3. Use sequence matcher for partial similarity
                score = SequenceMatcher(None, target_lower, candidate_lower).ratio()

            # 4. Bonus for similar length
            length_ratio = min(len(target_line), len(candidate)) / max(len(target_line), len(candidate))
            score *= (0.7 + 0.3 * length_ratio)

            if score > best_score and score > 0.6:  # Minimum threshold
                best_score = score
                best_index = i

        return best_index
    

    
    def _fuse_single_line(self, line_data: List[Tuple[str, float, str]]) -> Tuple[str, float]:
        """
        Intelligently fuse a single line from multiple OCR results
        Goal: Produce the best possible result with highest confidence
        """
        # Filter empty lines
        valid_lines = [(line, conf, model) for line, conf, model in line_data if line.strip()]

        if not valid_lines:
            return "", 0.0

        if len(valid_lines) == 1:
            return valid_lines[0][0], valid_lines[0][1]

        # Strategy 1: If lines are very similar, do character-level fusion
        lines_text = [line for line, _, _ in valid_lines]
        if self._are_lines_very_similar(lines_text):
            return self._intelligent_character_fusion(valid_lines)

        # Strategy 2: If one line clearly contains more information, use it
        # but boost confidence if other models agree on parts of it
        best_line = self._select_best_complete_line(valid_lines)
        enhanced_confidence = self._calculate_enhanced_confidence(best_line, valid_lines)

        return best_line[0], enhanced_confidence

    def _are_lines_very_similar(self, lines: List[str], threshold: float = 0.85) -> bool:
        """
        Check if lines are similar enough for character-level fusion
        """
        if len(lines) < 2:
            return True

        # Check all pairs
        for i in range(len(lines)):
            for j in range(i + 1, len(lines)):
                similarity = SequenceMatcher(None, lines[i].lower(), lines[j].lower()).ratio()
                if similarity < threshold:
                    return False
        return True

    def _select_best_complete_line(self, valid_lines: List[Tuple[str, float, str]]) -> Tuple[str, float, str]:
        """
        Select the most complete and reliable line
        """
        # Score each line based on length, confidence, and model reliability
        scored_lines = []

        for line, conf, model in valid_lines:
            # Base score from confidence
            score = conf

            # Length bonus (longer lines often more complete)
            length_bonus = min(len(line) / 50.0, 1.0) * 10  # Up to 10 points for length
            score += length_bonus

            # Model reliability bonus
            model_bonus = {'tesseract': 5, 'easyocr': 3, 'doctr': 3}.get(model, 0)
            score += model_bonus

            scored_lines.append((score, line, conf, model))

        # Return the highest scoring line
        best_scored = max(scored_lines, key=lambda x: x[0])
        return (best_scored[1], best_scored[2], best_scored[3])

    def _calculate_enhanced_confidence(self, best_line: Tuple[str, float, str], all_lines: List[Tuple[str, float, str]]) -> float:
        """
        Calculate enhanced confidence based on agreement between models
        """
        best_text, best_conf, best_model = best_line

        # Start with the original confidence
        enhanced_conf = best_conf

        # Check agreement with other models
        agreement_bonus = 0.0
        for line, conf, model in all_lines:
            if model != best_model:
                # Calculate how much this model agrees with the best line
                agreement = SequenceMatcher(None, best_text.lower(), line.lower()).ratio()

                # Add bonus based on agreement and the other model's confidence
                if agreement > 0.8:  # High agreement
                    agreement_bonus += (conf * agreement * 0.1)  # Up to 10% bonus per agreeing model
                elif agreement > 0.6:  # Moderate agreement
                    agreement_bonus += (conf * agreement * 0.05)  # Up to 5% bonus

        # Apply the bonus but cap at 99%
        enhanced_conf = min(enhanced_conf + agreement_bonus, 99.0)

        return enhanced_conf
    
    def _are_lines_similar(self, lines: List[str], threshold: float = 0.8) -> bool:
        """
        Vérifie si toutes les lignes sont suffisamment similaires
        """
        if len(lines) < 2:
            return True
        
        for i in range(len(lines)):
            for j in range(i + 1, len(lines)):
                similarity = SequenceMatcher(None, lines[i].lower(), lines[j].lower()).ratio()
                if similarity < threshold:
                    return False
        
        return True
    
    def _character_level_fusion(self, line_data: List[Tuple[str, float, str]]) -> Tuple[str, float]:
        """
        Fusion au niveau caractère pour des lignes différentes
        """
        # Aligner les chaînes de caractères
        lines = [line for line, _, _ in line_data]
        confidences = [conf for _, conf, _ in line_data]
        models = [model for _, _, model in line_data]
        
        # Trouver la longueur maximale
        max_length = max(len(line) for line in lines)
        
        # Étendre toutes les lignes à la même longueur
        padded_lines = []
        for line in lines:
            padded_lines.append(line.ljust(max_length))
        
        fused_chars = []
        char_confidences = []
        
        for pos in range(max_length):
            # Extraire les caractères à cette position
            chars_at_pos = []
            confs_at_pos = []
            models_at_pos = []
            
            for i, padded_line in enumerate(padded_lines):
                if pos < len(lines[i]):  # Caractère réel (pas de padding)
                    chars_at_pos.append(padded_line[pos])
                    confs_at_pos.append(confidences[i])
                    models_at_pos.append(models[i])
            
            if not chars_at_pos:
                continue
            
            # Choisir le meilleur caractère
            best_char, best_conf = self._choose_best_character(
                chars_at_pos, confs_at_pos, models_at_pos
            )
            
            if best_char != ' ' or (fused_chars and fused_chars[-1] != ' '):
                fused_chars.append(best_char)
                char_confidences.append(best_conf)
        
        # Construire la ligne finale
        fused_line = ''.join(fused_chars).strip()
        avg_confidence = sum(char_confidences) / len(char_confidences) if char_confidences else 0.0
        
        return fused_line, avg_confidence

    def _intelligent_character_fusion(self, line_data: List[Tuple[str, float, str]]) -> Tuple[str, float]:
        """
        Intelligent character-level fusion for very similar lines
        Focuses on correcting OCR errors while maintaining high confidence
        """
        lines = [line for line, _, _ in line_data]
        confidences = [conf for _, conf, _ in line_data]
        models = [model for _, _, model in line_data]

        # Find the highest confidence line as base
        base_line_idx = max(range(len(lines)), key=lambda i: confidences[i])
        base_line = lines[base_line_idx]
        base_conf = confidences[base_line_idx]

        # Character-by-character improvement
        improved_chars = []
        char_confidences = []

        for pos in range(len(base_line)):
            base_char = base_line[pos]

            # Collect characters at this position from all models
            chars_at_pos = [base_char]
            confs_at_pos = [base_conf]
            models_at_pos = [models[base_line_idx]]

            for i, line in enumerate(lines):
                if i != base_line_idx and pos < len(line):
                    chars_at_pos.append(line[pos])
                    confs_at_pos.append(confidences[i])
                    models_at_pos.append(models[i])

            # Choose best character with enhanced logic
            best_char, best_conf = self._choose_best_character_enhanced(
                chars_at_pos, confs_at_pos, models_at_pos, base_char
            )
            improved_chars.append(best_char)
            char_confidences.append(best_conf)

        fused_line = ''.join(improved_chars)

        # Calculate enhanced confidence - should be higher than base
        avg_char_conf = sum(char_confidences) / len(char_confidences) if char_confidences else base_conf
        enhanced_conf = max(base_conf, avg_char_conf)  # Never lower than original

        return fused_line, enhanced_conf

    def _choose_best_character_enhanced(self, chars: List[str], confs: List[float], models: List[str], base_char: str) -> Tuple[str, float]:
        """
        Enhanced character selection with OCR error correction
        """
        if not chars:
            return base_char, 0.0

        # If all characters are the same, return with highest confidence
        if len(set(chars)) == 1:
            return chars[0], max(confs)

        # Check for common OCR confusions and correct them
        corrected_char, correction_conf = self._apply_ocr_corrections(chars, confs, models)
        if corrected_char:
            return corrected_char, correction_conf

        # Otherwise, choose character with highest weighted score
        best_char = base_char
        best_score = 0.0

        for char, conf, model in zip(chars, confs, models):
            # Weight by confidence and model reliability
            model_weight = {'tesseract': 1.2, 'easyocr': 1.0, 'doctr': 1.0}.get(model, 1.0)
            score = conf * model_weight

            if score > best_score:
                best_score = score
                best_char = char

        return best_char, min(best_score, 99.0)

    def _apply_ocr_corrections(self, chars: List[str], confs: List[float], models: List[str]) -> Tuple[Optional[str], float]:
        """
        Apply intelligent OCR error corrections
        """
        # Count character occurrences with weighted confidence
        char_scores = {}
        for char, conf, model in zip(chars, confs, models):
            model_weight = {'tesseract': 1.2, 'easyocr': 1.0, 'doctr': 1.0}.get(model, 1.0)
            score = conf * model_weight

            if char in char_scores:
                char_scores[char] += score
            else:
                char_scores[char] = score

        # Find the best character
        if char_scores:
            best_char = max(char_scores.items(), key=lambda x: x[1])
            return best_char[0], min(best_char[1], 99.0)

        return None, 0.0

    def _choose_best_character(self, chars: List[str], confidences: List[float], models: List[str]) -> Tuple[str, float]:
        """
        Choisit le meilleur caractère parmi les candidats
        """
        if len(chars) == 1:
            return chars[0], confidences[0]
        
        # Compter les occurrences de chaque caractère
        char_votes = Counter(chars)
        
        # Si un caractère a la majorité absolue, le choisir
        most_common_char, count = char_votes.most_common(1)[0]
        if count > len(chars) / 2:
            # Trouver la meilleure confiance pour ce caractère
            best_conf = max(conf for char, conf in zip(chars, confidences) if char == most_common_char)
            return most_common_char, best_conf
        
        # Sinon, utiliser un système de vote pondéré
        char_scores = {}
        
        for char, conf, model in zip(chars, confidences, models):
            if char not in char_scores:
                char_scores[char] = 0.0
            
            # Score basé sur la confiance et le poids du modèle
            model_weight = self.character_weights.get(model, 1.0)
            
            # Bonus pour les caractères cohérents avec les patterns de confusion
            confusion_bonus = self._get_confusion_bonus(char, chars)
            
            score = conf * model_weight * (1 + confusion_bonus)
            char_scores[char] += score
        
        # Choisir le caractère avec le meilleur score
        best_char = max(char_scores.items(), key=lambda x: x[1])
        
        # Calculer la confiance finale
        final_conf = char_scores[best_char[0]] / len(chars)
        
        return best_char[0], min(99.0, final_conf)
    
    def _get_confusion_bonus(self, char: str, all_chars: List[str]) -> float:
        """
        Calcule un bonus basé sur les patterns de confusion OCR
        """
        bonus = 0.0
        
        # Si ce caractère est souvent confondu avec d'autres dans la liste
        if char in self.confusion_patterns:
            confused_chars = self.confusion_patterns[char]
            for other_char in all_chars:
                if other_char in confused_chars:
                    bonus += 0.1  # Petit bonus pour la cohérence
        
        return bonus
    
    def update_model_weights(self, performance_stats: Dict[str, float]):
        """
        Met à jour les poids des modèles basés sur leurs performances
        """
        total_performance = sum(performance_stats.values())
        
        if total_performance > 0:
            for model in self.character_weights:
                if model in performance_stats:
                    # Normaliser les poids basés sur les performances
                    self.character_weights[model] = performance_stats[model] / total_performance * 3
        
        logger.info(f"Poids des modèles mis à jour: {self.character_weights}")


def create_fusion_report(original_results: Dict, fused_lines: List[str], fused_confidences: List[float]) -> str:
    """
    Crée un rapport détaillé de la fusion
    """
    report = f"""
🔀 RAPPORT DE FUSION INTELLIGENTE
=================================

📊 Résultats Originaux:
• Tesseract: {len(original_results.get('tesseract', {}).get('lines', []))} lignes, {original_results.get('tesseract', {}).get('avg_conf', 0):.1f}% confiance
• EasyOCR: {len(original_results.get('easyocr', {}).get('lines', []))} lignes, {original_results.get('easyocr', {}).get('avg_conf', 0):.1f}% confiance  
• DocTR: {len(original_results.get('doctr', {}).get('lines', []))} lignes, {original_results.get('doctr', {}).get('avg_conf', 0):.1f}% confiance

🎯 Résultat Fusionné:
• Lignes fusionnées: {len(fused_lines)}
• Confiance moyenne: {sum(fused_confidences)/len(fused_confidences):.1f}% if fused_confidences else 0
• Caractères totaux: {sum(len(line) for line in fused_lines)}

🧠 Algorithme de Fusion:
• Alignement intelligent des lignes
• Fusion caractère par caractère
• Vote pondéré par confiance
• Gestion des patterns de confusion OCR
• Optimisation basée sur la structure du document

✨ Avantages:
• Combine les forces de chaque modèle
• Réduit les erreurs individuelles
• Améliore la précision globale
• Adaptatif selon le type de contenu
"""
    
    return report
