@echo off
setlocal enabledelayedexpansion

title OCR Intelligent - Quick Fix and Setup

echo ================================================================
echo OCR INTELLIGENT - QUICK FIX AND SETUP
echo ================================================================
echo This script will fix common issues and prepare the application
echo ================================================================
echo.

:: Check Python installation
echo [STEP 1] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo Then restart your computer and run this script again
    echo.
    pause
    exit /b 1
)

echo [OK] Python is available
python --version
echo.

:: Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python version: %PYTHON_VERSION%

:: Set Safran corporate proxy configuration
set PIP_INDEX_URL=https://artifacts.cloud.safran/repository/pypi-group/simple
set PIP_TRUSTED_HOST=artifacts.cloud.safran
set SAFRAN_PIP_ARGS=--index-url %PIP_INDEX_URL% --trusted-host %PIP_TRUSTED_HOST%

echo [INFO] Using Safran corporate proxy: %PIP_INDEX_URL%

:: Upgrade pip first
echo [STEP 2] Upgrading pip...
python -m pip install --upgrade pip --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
if errorlevel 1 (
    echo [WARNING] pip upgrade failed, continuing anyway...
) else (
    echo [OK] pip upgraded successfully
)

:: Install critical dependencies
echo [STEP 3] Installing critical dependencies...

echo Installing Streamlit...
python -m pip install streamlit --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
if errorlevel 1 (
    echo [WARNING] System installation failed, trying user installation...
    python -m pip install streamlit --user --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
    if errorlevel 1 (
        echo [ERROR] Failed to install Streamlit
        echo Please check your corporate network connection
        pause
        exit /b 1
    )
)
echo [OK] Streamlit installed

echo Installing psutil...
python -m pip install psutil --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
if errorlevel 1 (
    echo [WARNING] System installation failed, trying user installation...
    python -m pip install psutil --user --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
    if errorlevel 1 (
        echo [WARNING] psutil installation failed - process management will be limited
    )
)
echo [OK] psutil installed

:: Install all requirements
echo [STEP 4] Installing all project dependencies...
if exist requirements.txt (
    echo This may take several minutes...
    echo [INFO] Installing from Safran corporate repository...
    python -m pip install -r requirements.txt --quiet --disable-pip-version-check --no-warn-script-location %SAFRAN_PIP_ARGS%
    if errorlevel 1 (
        echo [WARNING] Some dependencies failed, trying user installation...
        python -m pip install -r requirements.txt --user --quiet --disable-pip-version-check --no-warn-script-location %SAFRAN_PIP_ARGS%
        if errorlevel 1 (
            echo [WARNING] Some dependencies could not be installed
            echo The application may have limited functionality
            echo [INFO] This is normal in corporate environments with package restrictions
        )
    )
    echo [OK] Dependencies processed
) else (
    echo [WARNING] requirements.txt not found
)

:: Create necessary directories
echo [STEP 5] Creating working directories...
if not exist output mkdir output
if not exist logs mkdir logs
if not exist corrected mkdir corrected
echo [OK] Working directories created

:: Verify installation
echo [STEP 6] Verifying installation...
python -c "import streamlit; print(f'Streamlit {streamlit.__version__} is ready')" 2>nul
if errorlevel 1 (
    echo [ERROR] Streamlit verification failed
    echo Please run this script again or install manually
    pause
    exit /b 1
)

python -c "import psutil; print(f'psutil {psutil.__version__} is ready')" 2>nul
if errorlevel 1 (
    echo [WARNING] psutil not available - process management will be limited
) else (
    echo [OK] psutil is ready
)

:: Test port management
echo [STEP 7] Testing port management...
python -c "import socket; s = socket.socket(); s.bind(('localhost', 0)); print(f'Port test successful: {s.getsockname()[1]}'); s.close()" 2>nul
if errorlevel 1 (
    echo [WARNING] Port management test failed
) else (
    echo [OK] Port management is working
)

:: Final summary
echo.
echo ================================================================
echo SETUP COMPLETE
echo ================================================================
echo.
echo Status Summary:
echo - Python: Available (%PYTHON_VERSION%)
echo - Streamlit: Installed and verified
echo - Dependencies: Processed
echo - Working directories: Created
echo.
echo You can now:
echo 1. Run "Lancer_OCR_Intelligent.bat" to start the application
echo 2. Run "check_installer.bat" to verify installer prerequisites
echo 3. Run "build_installer.bat" to create the .exe installer (if Inno Setup is installed)
echo.
echo If you encounter issues:
echo - Check the logs/ directory for error details
echo - Run "python verify_dependencies.py" for detailed dependency check
echo - Consult README.md for troubleshooting guide
echo.

choice /C YN /M "Do you want to start the OCR Intelligent application now"
if not errorlevel 2 (
    echo.
    echo Starting OCR Intelligent...
    call Lancer_OCR_Intelligent.bat
)

echo.
echo Thank you for using OCR Intelligent!
pause
