@echo off
title Test OCR Intelligent

echo ================================================================
echo TEST DE DIAGNOSTIC OCR INTELLIGENT
echo ================================================================
echo.

:: Test 1: Python
echo [TEST 1] Verification de Python...
python --version
if errorlevel 1 (
    echo ECHEC: Python non trouve
    goto :error
) else (
    echo SUCCES: Python detecte
)
echo.

:: Test 2: Fichiers principaux
echo [TEST 2] Verification des fichiers...
if exist main.py (
    echo SUCCES: main.py trouve
) else (
    echo ECHEC: main.py manquant
    goto :error
)

if exist frontend\app.py (
    echo SUCCES: frontend\app.py trouve
) else (
    echo ECHEC: frontend\app.py manquant
    goto :error
)

if exist backend\main.py (
    echo SUCCES: backend\main.py trouve
) else (
    echo ECHEC: backend\main.py manquant
    goto :error
)
echo.

:: Test 3: Streamlit
echo [TEST 3] Test de Streamlit...
python -c "import streamlit; print('Streamlit version:', streamlit.__version__)"
if errorlevel 1 (
    echo ECHEC: Streamlit non disponible
    echo Tentative d'installation...
    python -m pip install streamlit --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
    if errorlevel 1 (
        echo ECHEC: Installation de Streamlit echouee
        goto :error
    )
) else (
    echo SUCCES: Streamlit disponible
)
echo.

:: Test 4: Lancement de main.py
echo [TEST 4] Test de lancement de main.py...
echo Appuyez sur Ctrl+C apres quelques secondes pour arreter le test
echo.
timeout /t 3 /nobreak
python main.py

echo.
echo ================================================================
echo TESTS TERMINES
echo ================================================================
echo.
echo Si tous les tests sont SUCCES, utilisez:
echo - Lancer_OCR_Intelligent.bat (version complete)
echo - Lancer_Simple.bat (version simplifiee)
echo.
pause
exit /b 0

:error
echo.
echo ================================================================
echo ERREUR DETECTEE
echo ================================================================
echo.
echo Verifiez:
echo 1. Python est installe avec PATH configure
echo 2. Tous les fichiers du projet sont presents
echo 3. Connexion reseau pour installer Streamlit
echo.
pause
exit /b 1
