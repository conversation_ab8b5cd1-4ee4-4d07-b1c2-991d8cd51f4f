@echo off
setlocal enabledelayedexpansion

title OCR Intelligent - Batch File Tester

echo ================================================================
echo OCR INTELLIGENT - BATCH FILE TESTER
echo ================================================================
echo This script will test if the batch files are working correctly
echo ================================================================
echo.

echo [INFO] Testing batch file functionality...
echo [INFO] Current directory: %CD%
echo [INFO] Current time: %DATE% %TIME%
echo.

:: Test 1: Check if batch files exist
echo [TEST 1] Checking if batch files exist...
set BATCH_FILES_OK=1

if exist "Lancer_OCR_Intelligent.bat" (
    echo [OK] Lancer_OCR_Intelligent.bat exists
) else (
    echo [ERROR] Lancer_OCR_Intelligent.bat not found
    set BATCH_FILES_OK=0
)

if exist "build_installer.bat" (
    echo [OK] build_installer.bat exists
) else (
    echo [ERROR] build_installer.bat not found
    set BATCH_FILES_OK=0
)

if exist "fix_ocr_intelligent.bat" (
    echo [OK] fix_ocr_intelligent.bat exists
) else (
    echo [WARNING] fix_ocr_intelligent.bat not found
)

echo.

:: Test 2: Check batch file syntax
echo [TEST 2] Testing batch file syntax...

echo [INFO] Testing Lancer_OCR_Intelligent.bat syntax...
call :test_batch_syntax "Lancer_OCR_Intelligent.bat"

echo [INFO] Testing build_installer.bat syntax...
call :test_batch_syntax "build_installer.bat"

echo.

:: Test 3: Check Python availability
echo [TEST 3] Checking Python availability...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not available
    echo This will cause Lancer_OCR_Intelligent.bat to fail
) else (
    echo [OK] Python is available
    python --version
)

echo.

:: Test 4: Check basic environment
echo [TEST 4] Checking environment...
echo [INFO] PATH length: 
echo %PATH% | find /c ";" 
echo [INFO] Current user: %USERNAME%
echo [INFO] Admin rights: 
net session >nul 2>&1
if errorlevel 1 (
    echo   No (running as regular user)
) else (
    echo   Yes (running as administrator)
)

echo.

:: Test 5: Manual execution test
echo [TEST 5] Manual execution test...
if %BATCH_FILES_OK%==1 (
    echo [INFO] All batch files found. You can now test them manually:
    echo.
    echo Option 1: Test the main launcher
    echo   - Double-click on "debug_launcher.bat" for detailed diagnostics
    echo   - Or double-click on "Lancer_OCR_Intelligent.bat" to run normally
    echo.
    echo Option 2: Test the installer builder  
    echo   - Double-click on "debug_installer.bat" for detailed diagnostics
    echo   - Or double-click on "build_installer.bat" to build normally
    echo.
    echo Option 3: Use the command prompt
    echo   - Open Command Prompt in this directory
    echo   - Run: Lancer_OCR_Intelligent.bat
    echo   - Or run: build_installer.bat
    echo.
) else (
    echo [ERROR] Some batch files are missing
    echo Please ensure all files are present before testing
)

echo ================================================================
echo BATCH FILE TEST COMPLETE
echo ================================================================
echo.
echo Summary:
echo - Batch files exist: %BATCH_FILES_OK% (1=yes, 0=no)
echo - Python available: 
python --version >nul 2>&1 && echo   Yes || echo   No
echo.
echo Next steps:
echo 1. If batch files close immediately, use the debug versions
echo 2. Run "debug_launcher.bat" to diagnose launcher issues
echo 3. Run "debug_installer.bat" to diagnose installer issues
echo 4. Check that you have all required files in the project directory
echo.
echo Press any key to close...
pause >nul
exit /b 0

:: Function to test batch file syntax
:test_batch_syntax
set "batch_file=%~1"
if exist "%batch_file%" (
    :: Try to parse the batch file for obvious syntax errors
    findstr /i "goto :eof" "%batch_file%" >nul
    if errorlevel 1 (
        echo [WARNING] %batch_file% may have syntax issues (no proper exit)
    ) else (
        echo [OK] %batch_file% syntax appears correct
    )
) else (
    echo [ERROR] %batch_file% not found for syntax check
)
goto :eof
