@echo off
setlocal enabledelayedexpansion

title OCR Intelligent - Safran Environment Setup

echo ================================================================
echo OCR INTELLIGENT - SAFRAN CORPORATE ENVIRONMENT SETUP
echo ================================================================
echo This script configures OCR Intelligent for Safran corporate network
echo ================================================================
echo.

:: Set Safran corporate proxy configuration
set PIP_INDEX_URL=https://artifacts.cloud.safran/repository/pypi-group/simple
set PIP_TRUSTED_HOST=artifacts.cloud.safran
set PIP_TIMEOUT=60
set SAFRAN_PIP_ARGS=--index-url %PIP_INDEX_URL% --trusted-host %PIP_TRUSTED_HOST% --timeout %PIP_TIMEOUT%

echo [INFO] Safran Corporate Proxy Configuration:
echo   Index URL: %PIP_INDEX_URL%
echo   Trusted Host: %PIP_TRUSTED_HOST%
echo   Timeout: %PIP_TIMEOUT% seconds
echo.

:: Set environment variables for this session
set PIP_DISABLE_PIP_VERSION_CHECK=1
set PYTHONIOENCODING=utf-8

echo [STEP 1] Testing corporate network connectivity...
python -c "import urllib.request; urllib.request.urlopen('%PIP_INDEX_URL%', timeout=10); print('✓ Corporate repository accessible')" 2>nul
if errorlevel 1 (
    echo [WARNING] Cannot reach corporate repository
    echo This may be normal if you're not connected to Safran network
    echo The scripts will still work when connected
) else (
    echo [OK] Corporate repository is accessible
)

echo.
echo [STEP 2] Configuring pip for Safran environment...

:: Create or update pip configuration
if not exist "%APPDATA%\pip" mkdir "%APPDATA%\pip"

echo [global] > "%APPDATA%\pip\pip.ini"
echo index-url = %PIP_INDEX_URL% >> "%APPDATA%\pip\pip.ini"
echo trusted-host = %PIP_TRUSTED_HOST% >> "%APPDATA%\pip\pip.ini"
echo timeout = %PIP_TIMEOUT% >> "%APPDATA%\pip\pip.ini"
echo disable-pip-version-check = true >> "%APPDATA%\pip\pip.ini"

echo [OK] Pip configuration updated for Safran environment
echo Configuration saved to: %APPDATA%\pip\pip.ini

echo.
echo [STEP 3] Testing pip with Safran configuration...
python -m pip list --format=freeze | findstr streamlit >nul 2>&1
if not errorlevel 1 (
    echo [OK] Streamlit is already installed
) else (
    echo [INFO] Testing pip installation with corporate proxy...
    python -m pip install --dry-run streamlit %SAFRAN_PIP_ARGS% >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Pip test failed - check network connectivity
    ) else (
        echo [OK] Pip can access corporate repository
    )
)

echo.
echo [STEP 4] Creating Safran-specific launcher...

:: Create a Safran-specific launcher that sets environment variables
echo @echo off > Lancer_OCR_Safran.bat
echo setlocal enabledelayedexpansion >> Lancer_OCR_Safran.bat
echo. >> Lancer_OCR_Safran.bat
echo title OCR Intelligent - Safran Environment >> Lancer_OCR_Safran.bat
echo. >> Lancer_OCR_Safran.bat
echo :: Safran Corporate Environment Configuration >> Lancer_OCR_Safran.bat
echo set PIP_INDEX_URL=%PIP_INDEX_URL% >> Lancer_OCR_Safran.bat
echo set PIP_TRUSTED_HOST=%PIP_TRUSTED_HOST% >> Lancer_OCR_Safran.bat
echo set PIP_TIMEOUT=%PIP_TIMEOUT% >> Lancer_OCR_Safran.bat
echo set PIP_DISABLE_PIP_VERSION_CHECK=1 >> Lancer_OCR_Safran.bat
echo set PYTHONIOENCODING=utf-8 >> Lancer_OCR_Safran.bat
echo. >> Lancer_OCR_Safran.bat
echo echo [INFO] Using Safran corporate environment configuration >> Lancer_OCR_Safran.bat
echo echo [INFO] Proxy: %%PIP_INDEX_URL%% >> Lancer_OCR_Safran.bat
echo echo. >> Lancer_OCR_Safran.bat
echo. >> Lancer_OCR_Safran.bat
echo :: Launch the main application >> Lancer_OCR_Safran.bat
echo call Lancer_OCR_Intelligent.bat >> Lancer_OCR_Safran.bat

echo [OK] Safran-specific launcher created: Lancer_OCR_Safran.bat

echo.
echo [STEP 5] Generating status report...
python safran_config.py 2>nul
if errorlevel 1 (
    echo [INFO] Safran configuration module not found - this is optional
)

echo.
echo ================================================================
echo SAFRAN ENVIRONMENT SETUP COMPLETE
echo ================================================================
echo.
echo Configuration Summary:
echo - Pip configured for Safran corporate repository
echo - Environment variables set for current session
echo - Safran-specific launcher created
echo - All OCR Intelligent scripts updated with proxy support
echo.
echo Usage Options:
echo 1. Use "Lancer_OCR_Safran.bat" (recommended for Safran network)
echo 2. Use "Lancer_OCR_Intelligent.bat" (already configured with proxy)
echo 3. Use "fix_ocr_intelligent.bat" for troubleshooting
echo.
echo Known Safe Warnings in Safran Environment:
echo - Invalid distributions (~illow, ~pencv-python): Cosmetic only
echo - Connection retry warnings: Normal corporate network behavior
echo - python-doctr torch extra warning: Uses simulation mode
echo.
echo These warnings do not affect OCR Intelligent functionality.
echo.

choice /C YN /M "Do you want to test the OCR Intelligent application now"
if not errorlevel 2 (
    echo.
    echo Testing OCR Intelligent with Safran configuration...
    call Lancer_OCR_Safran.bat
)

echo.
echo Setup complete! You can now use OCR Intelligent in the Safran environment.
pause
