"""
Module de fusion intelligente multi-modèles OCR
Combine les résultats de Tesseract, EasyOCR et DocTR pour produire un résultat optimal
"""
import re
import logging
from typing import List, Tuple, Dict, Optional
from difflib import SequenceMatcher
from collections import Counter
import numpy as np

logger = logging.getLogger(__name__)


class IntelligentOCRFusion:
    """
    Classe pour la fusion intelligente des résultats OCR multi-modèles
    """
    
    def __init__(self):
        self.character_weights = {
            'tesseract': 1.0,
            'easyocr': 1.0,
            'doctr': 1.0
        }
        
        # Patterns de caractères souvent confondus par l'OCR
        self.confusion_patterns = {
            'I': ['l', '1', '|'],
            'l': ['I', '1', '|'],
            '1': ['I', 'l', '|'],
            'O': ['0', 'Q'],
            '0': ['O', 'Q'],
            'rn': ['m'],
            'm': ['rn'],
            'cl': ['d'],
            'vv': ['w'],
            'w': ['vv']
        }
    
    def fuse_ocr_results(self, results: Dict[str, Dict]) -> Tuple[List[str], List[float]]:
        """
        Fusionne intelligemment les résultats des 3 modèles OCR
        
        Args:
            results: Dictionnaire contenant les résultats de chaque modèle
                    Format: {'tesseract': {'lines': [...], 'confs': [...]}, ...}
        
        Returns:
            Tuple (lignes_fusionnées, scores_confiance)
        """
        logger.info("Début de la fusion intelligente multi-modèles")
        
        # Extraire les résultats de chaque modèle
        tesseract_lines = results.get('tesseract', {}).get('lines', [])
        tesseract_confs = results.get('tesseract', {}).get('confs', [])
        
        easyocr_lines = results.get('easyocr', {}).get('lines', [])
        easyocr_confs = results.get('easyocr', {}).get('confs', [])
        
        doctr_lines = results.get('doctr', {}).get('lines', [])
        doctr_confs = results.get('doctr', {}).get('confs', [])
        
        # Aligner les lignes des différents modèles
        aligned_lines = self._align_lines(
            tesseract_lines, easyocr_lines, doctr_lines
        )
        
        # Fusionner ligne par ligne
        fused_lines = []
        fused_confidences = []
        
        for i, (tess_line, easy_line, doctr_line) in enumerate(aligned_lines):
            # Récupérer les scores de confiance correspondants de manière sécurisée
            tess_conf = 0.0
            easy_conf = 0.0
            doctr_conf = 0.0

            # Trouver la confiance pour Tesseract
            if tess_line.strip() and tesseract_lines and tesseract_confs:
                try:
                    tess_idx = tesseract_lines.index(tess_line) if tess_line in tesseract_lines else min(i, len(tesseract_confs)-1)
                    tess_conf = tesseract_confs[tess_idx] if tess_idx < len(tesseract_confs) else 75.0
                except (ValueError, IndexError):
                    tess_conf = 75.0  # Confiance par défaut

            # Trouver la confiance pour EasyOCR
            if easy_line.strip() and easyocr_lines and easyocr_confs:
                try:
                    easy_idx = easyocr_lines.index(easy_line) if easy_line in easyocr_lines else min(i, len(easyocr_confs)-1)
                    easy_conf = easyocr_confs[easy_idx] if easy_idx < len(easyocr_confs) else 75.0
                except (ValueError, IndexError):
                    easy_conf = 75.0

            # Trouver la confiance pour DocTR
            if doctr_line.strip() and doctr_lines and doctr_confs:
                try:
                    doctr_idx = doctr_lines.index(doctr_line) if doctr_line in doctr_lines else min(i, len(doctr_confs)-1)
                    doctr_conf = doctr_confs[doctr_idx] if doctr_idx < len(doctr_confs) else 75.0
                except (ValueError, IndexError):
                    doctr_conf = 75.0

            # Fusionner cette ligne spécifique
            fused_line, fused_conf = self._fuse_single_line(
                [(tess_line, tess_conf, 'tesseract'),
                 (easy_line, easy_conf, 'easyocr'),
                 (doctr_line, doctr_conf, 'doctr')]
            )

            # Ajouter toutes les lignes non vides (plus permissif)
            if fused_line.strip():
                fused_lines.append(fused_line)
                fused_confidences.append(fused_conf)
        
        logger.info(f"Fusion terminée: {len(fused_lines)} lignes fusionnées")
        return fused_lines, fused_confidences
    
    def _align_lines(self, lines1: List[str], lines2: List[str], lines3: List[str]) -> List[Tuple[str, str, str]]:
        """
        Aligne les lignes des trois modèles en conservant TOUTES les informations
        Approche ultra-conservative : ne perdre AUCUNE information
        """
        aligned_groups = []

        # Stratégie 1: Alignement direct par index (le plus simple et sûr)
        max_lines = max(len(lines1), len(lines2), len(lines3))

        for i in range(max_lines):
            tess_line = lines1[i] if i < len(lines1) else ""
            easy_line = lines2[i] if i < len(lines2) else ""
            doctr_line = lines3[i] if i < len(lines3) else ""

            aligned_groups.append((tess_line, easy_line, doctr_line))

        # Stratégie 2: Ajouter TOUTES les lignes uniques qui pourraient avoir été manquées
        # Créer un ensemble de toutes les lignes déjà alignées
        aligned_content = set()
        for tess, easy, doctr in aligned_groups:
            if tess.strip():
                aligned_content.add(tess.strip())
            if easy.strip():
                aligned_content.add(easy.strip())
            if doctr.strip():
                aligned_content.add(doctr.strip())

        # Vérifier chaque source pour des lignes manquantes
        all_sources = [
            (lines1, 'tesseract'),
            (lines2, 'easyocr'),
            (lines3, 'doctr')
        ]

        for source_lines, source_name in all_sources:
            for line in source_lines:
                if line.strip() and line.strip() not in aligned_content:
                    # Cette ligne n'est pas encore représentée, l'ajouter
                    if source_name == 'tesseract':
                        aligned_groups.append((line, "", ""))
                    elif source_name == 'easyocr':
                        aligned_groups.append(("", line, ""))
                    else:  # doctr
                        aligned_groups.append(("", "", line))

                    aligned_content.add(line.strip())

        return aligned_groups
    

    
    def _fuse_single_line(self, line_data: List[Tuple[str, float, str]]) -> Tuple[str, float]:
        """
        Fusionne une seule ligne en utilisant les résultats des 3 modèles
        Approche conservative : préserver la meilleure information complète
        """
        # Filtrer les lignes vides
        valid_lines = [(line, conf, model) for line, conf, model in line_data if line.strip()]

        if not valid_lines:
            return "", 0.0

        if len(valid_lines) == 1:
            return valid_lines[0][0], valid_lines[0][1]

        # Stratégie 1: Privilégier la ligne la plus complète avec une bonne confiance
        # Trier par longueur décroissante puis par confiance décroissante
        sorted_lines = sorted(valid_lines, key=lambda x: (len(x[0].strip()), x[1]), reverse=True)

        # Prendre la ligne la plus longue si elle a une confiance raisonnable (>60%)
        longest_line = sorted_lines[0]
        if longest_line[1] > 60.0:
            return longest_line[0], longest_line[1]

        # Stratégie 2: Si la ligne la plus longue a une faible confiance,
        # vérifier si une ligne plus courte mais plus fiable existe
        for line, conf, model in sorted_lines[1:]:
            if conf > longest_line[1] + 10.0:  # Au moins 10% de confiance en plus
                # Vérifier que cette ligne n'est pas trop courte par rapport à la plus longue
                if len(line.strip()) >= len(longest_line[0].strip()) * 0.7:  # Au moins 70% de la longueur
                    return line, conf

        # Stratégie 3: Si les lignes sont similaires, faire une fusion fine
        lines_text = [line for line, _, _ in valid_lines]
        if self._are_lines_similar(lines_text, threshold=0.7):  # Seuil plus permissif
            return self._character_level_fusion(valid_lines)

        # Stratégie 4: Par défaut, prendre la ligne avec la meilleure confiance
        best_conf_line = max(valid_lines, key=lambda x: x[1])
        return best_conf_line[0], best_conf_line[1]
    
    def _are_lines_similar(self, lines: List[str], threshold: float = 0.8) -> bool:
        """
        Vérifie si toutes les lignes sont suffisamment similaires
        """
        if len(lines) < 2:
            return True
        
        for i in range(len(lines)):
            for j in range(i + 1, len(lines)):
                similarity = SequenceMatcher(None, lines[i].lower(), lines[j].lower()).ratio()
                if similarity < threshold:
                    return False
        
        return True
    
    def _character_level_fusion(self, line_data: List[Tuple[str, float, str]]) -> Tuple[str, float]:
        """
        Fusion au niveau caractère pour des lignes différentes
        """
        # Aligner les chaînes de caractères
        lines = [line for line, _, _ in line_data]
        confidences = [conf for _, conf, _ in line_data]
        models = [model for _, _, model in line_data]
        
        # Trouver la longueur maximale
        max_length = max(len(line) for line in lines)
        
        # Étendre toutes les lignes à la même longueur
        padded_lines = []
        for line in lines:
            padded_lines.append(line.ljust(max_length))
        
        fused_chars = []
        char_confidences = []
        
        for pos in range(max_length):
            # Extraire les caractères à cette position
            chars_at_pos = []
            confs_at_pos = []
            models_at_pos = []
            
            for i, padded_line in enumerate(padded_lines):
                if pos < len(lines[i]):  # Caractère réel (pas de padding)
                    chars_at_pos.append(padded_line[pos])
                    confs_at_pos.append(confidences[i])
                    models_at_pos.append(models[i])
            
            if not chars_at_pos:
                continue
            
            # Choisir le meilleur caractère
            best_char, best_conf = self._choose_best_character(
                chars_at_pos, confs_at_pos, models_at_pos
            )
            
            if best_char != ' ' or (fused_chars and fused_chars[-1] != ' '):
                fused_chars.append(best_char)
                char_confidences.append(best_conf)
        
        # Construire la ligne finale
        fused_line = ''.join(fused_chars).strip()
        avg_confidence = sum(char_confidences) / len(char_confidences) if char_confidences else 0.0
        
        return fused_line, avg_confidence
    
    def _choose_best_character(self, chars: List[str], confidences: List[float], models: List[str]) -> Tuple[str, float]:
        """
        Choisit le meilleur caractère parmi les candidats
        """
        if len(chars) == 1:
            return chars[0], confidences[0]
        
        # Compter les occurrences de chaque caractère
        char_votes = Counter(chars)
        
        # Si un caractère a la majorité absolue, le choisir
        most_common_char, count = char_votes.most_common(1)[0]
        if count > len(chars) / 2:
            # Trouver la meilleure confiance pour ce caractère
            best_conf = max(conf for char, conf in zip(chars, confidences) if char == most_common_char)
            return most_common_char, best_conf
        
        # Sinon, utiliser un système de vote pondéré
        char_scores = {}
        
        for char, conf, model in zip(chars, confidences, models):
            if char not in char_scores:
                char_scores[char] = 0.0
            
            # Score basé sur la confiance et le poids du modèle
            model_weight = self.character_weights.get(model, 1.0)
            
            # Bonus pour les caractères cohérents avec les patterns de confusion
            confusion_bonus = self._get_confusion_bonus(char, chars)
            
            score = conf * model_weight * (1 + confusion_bonus)
            char_scores[char] += score
        
        # Choisir le caractère avec le meilleur score
        best_char = max(char_scores.items(), key=lambda x: x[1])
        
        # Calculer la confiance finale
        final_conf = char_scores[best_char[0]] / len(chars)
        
        return best_char[0], min(99.0, final_conf)
    
    def _get_confusion_bonus(self, char: str, all_chars: List[str]) -> float:
        """
        Calcule un bonus basé sur les patterns de confusion OCR
        """
        bonus = 0.0
        
        # Si ce caractère est souvent confondu avec d'autres dans la liste
        if char in self.confusion_patterns:
            confused_chars = self.confusion_patterns[char]
            for other_char in all_chars:
                if other_char in confused_chars:
                    bonus += 0.1  # Petit bonus pour la cohérence
        
        return bonus
    
    def update_model_weights(self, performance_stats: Dict[str, float]):
        """
        Met à jour les poids des modèles basés sur leurs performances
        """
        total_performance = sum(performance_stats.values())
        
        if total_performance > 0:
            for model in self.character_weights:
                if model in performance_stats:
                    # Normaliser les poids basés sur les performances
                    self.character_weights[model] = performance_stats[model] / total_performance * 3
        
        logger.info(f"Poids des modèles mis à jour: {self.character_weights}")


def create_fusion_report(original_results: Dict, fused_lines: List[str], fused_confidences: List[float]) -> str:
    """
    Crée un rapport détaillé de la fusion
    """
    report = f"""
🔀 RAPPORT DE FUSION INTELLIGENTE
=================================

📊 Résultats Originaux:
• Tesseract: {len(original_results.get('tesseract', {}).get('lines', []))} lignes, {original_results.get('tesseract', {}).get('avg_conf', 0):.1f}% confiance
• EasyOCR: {len(original_results.get('easyocr', {}).get('lines', []))} lignes, {original_results.get('easyocr', {}).get('avg_conf', 0):.1f}% confiance  
• DocTR: {len(original_results.get('doctr', {}).get('lines', []))} lignes, {original_results.get('doctr', {}).get('avg_conf', 0):.1f}% confiance

🎯 Résultat Fusionné:
• Lignes fusionnées: {len(fused_lines)}
• Confiance moyenne: {sum(fused_confidences)/len(fused_confidences):.1f}% if fused_confidences else 0
• Caractères totaux: {sum(len(line) for line in fused_lines)}

🧠 Algorithme de Fusion:
• Alignement intelligent des lignes
• Fusion caractère par caractère
• Vote pondéré par confiance
• Gestion des patterns de confusion OCR
• Optimisation basée sur la structure du document

✨ Avantages:
• Combine les forces de chaque modèle
• Réduit les erreurs individuelles
• Améliore la précision globale
• Adaptatif selon le type de contenu
"""
    
    return report
