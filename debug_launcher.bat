@echo off
setlocal enabledelayedexpansion

title OCR Intelligent - Debug Launcher

echo ================================================================
echo OCR INTELLIGENT - DEBUG LAUNCHER
echo ================================================================
echo This script will help diagnose issues with the main launcher
echo ================================================================
echo.

:: Enable verbose output
set DEBUG_MODE=1

echo [DEBUG] Starting diagnostic checks...
echo [DEBUG] Current directory: %CD%
echo [DEBUG] Current time: %DATE% %TIME%
echo [DEBUG] User: %USERNAME%
echo [DEBUG] Computer: %COMPUTERNAME%
echo.

:: Check if main files exist
echo [STEP 1] Checking for required files...
set FILES_OK=1

if exist "Lancer_OCR_Intelligent.bat" (
    echo [OK] Lancer_OCR_Intelligent.bat found
) else (
    echo [ERROR] Lancer_OCR_Intelligent.bat not found
    set FILES_OK=0
)

if exist "main.py" (
    echo [OK] main.py found
) else (
    echo [ERROR] main.py not found
    set FILES_OK=0
)

if exist "frontend\app.py" (
    echo [OK] frontend\app.py found
) else (
    echo [ERROR] frontend\app.py not found
    set FILES_OK=0
)

if exist "requirements.txt" (
    echo [OK] requirements.txt found
) else (
    echo [WARNING] requirements.txt not found
)

echo.

:: Check Python
echo [STEP 2] Checking Python installation...
where python >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found in PATH
    echo [DEBUG] Searching for Python installations...
    
    :: Search common Python locations
    for /d %%i in ("C:\Python*") do (
        if exist "%%i\python.exe" (
            echo [INFO] Found Python at: %%i\python.exe
        )
    )
    
    for /d %%i in ("%LOCALAPPDATA%\Programs\Python\Python*") do (
        if exist "%%i\python.exe" (
            echo [INFO] Found Python at: %%i\python.exe
        )
    )
    
    echo.
    echo [ERROR] Python is not accessible from command line
    echo Please install Python or add it to your PATH
    goto :error_exit
) else (
    echo [OK] Python found in PATH
    python --version
    echo [DEBUG] Python executable location:
    where python
)

echo.

:: Check if we can import basic modules
echo [STEP 3] Testing Python modules...
python -c "import sys; print(f'Python executable: {sys.executable}')"
python -c "import sys; print(f'Python path: {sys.path[0]}')"

echo [DEBUG] Testing streamlit import...
python -c "import streamlit; print(f'Streamlit version: {streamlit.__version__}')" 2>nul
if errorlevel 1 (
    echo [WARNING] Streamlit not available
) else (
    echo [OK] Streamlit is available
)

echo.

:: Test the main launcher
echo [STEP 4] Testing main launcher...
if %FILES_OK%==1 (
    echo [INFO] All required files found, attempting to run main launcher...
    echo [INFO] This will run Lancer_OCR_Intelligent.bat in debug mode...
    echo.
    echo Press any key to continue or Ctrl+C to cancel...
    pause >nul
    
    echo [DEBUG] Calling main launcher...
    call "Lancer_OCR_Intelligent.bat" DEBUG
    
    echo.
    echo [DEBUG] Main launcher returned with exit code: %ERRORLEVEL%
) else (
    echo [ERROR] Cannot run main launcher - required files missing
)

echo.
echo ================================================================
echo DEBUG SESSION COMPLETE
echo ================================================================
echo.
echo If you're still having issues:
echo 1. Check that Python is properly installed and in PATH
echo 2. Ensure all project files are in the correct location
echo 3. Try running "fix_ocr_intelligent.bat" to install dependencies
echo 4. Check the logs\ directory for detailed error information
echo.

:error_exit
echo Press any key to close this window...
pause >nul
exit /b 0
