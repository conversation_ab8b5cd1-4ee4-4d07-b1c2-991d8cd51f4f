#!/usr/bin/env python3
"""
Dependency verification and installation script for OCR Intelligent
Compatible with Python 3.8+ including Python 3.13
Configured for Safran corporate environment with proxy support
"""
import sys
import subprocess
import importlib
import os
from pathlib import Path

# Safran corporate proxy configuration
SAFRAN_INDEX_URL = "https://artifacts.cloud.safran/repository/pypi-group/simple"
SAFRAN_TRUSTED_HOST = "artifacts.cloud.safran"
SAFRAN_PIP_ARGS = [
    "--index-url", SAFRAN_INDEX_URL,
    "--trusted-host", SAFRAN_TRUSTED_HOST
]

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("ERROR: Python 3.8 or higher is required")
        return False
    
    if version.major == 3 and version.minor >= 13:
        print("INFO: Python 3.13+ detected - using compatible installation methods")
    
    return True

def install_package(package_name, user_install=False):
    """Install a Python package with Safran corporate proxy support"""
    try:
        cmd = [sys.executable, "-m", "pip", "install", package_name, "--quiet", "--no-warn-script-location"]
        cmd.extend(SAFRAN_PIP_ARGS)  # Add Safran proxy configuration

        if user_install:
            cmd.append("--user")

        print(f"Installing {package_name} from Safran corporate repository...")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✓ {package_name} installed successfully")
            return True
        else:
            print(f"✗ Failed to install {package_name}: {result.stderr}")
            return False

    except Exception as e:
        print(f"✗ Error installing {package_name}: {e}")
        return False

def check_and_install_dependency(package_name, import_name=None):
    """Check if a package is installed, install if missing"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} is available")
        return True
    except ImportError:
        print(f"✗ {package_name} is missing, attempting installation...")
        
        # Try regular installation first
        if install_package(package_name):
            return True
        
        # Try user installation as fallback
        print(f"Trying user installation for {package_name}...")
        if install_package(package_name, user_install=True):
            return True
        
        print(f"✗ Failed to install {package_name}")
        return False

def upgrade_pip():
    """Upgrade pip to latest version using Safran corporate proxy"""
    try:
        print("Upgrading pip using Safran corporate repository...")
        cmd = [sys.executable, "-m", "pip", "install", "--upgrade", "pip", "--quiet"]
        cmd.extend(SAFRAN_PIP_ARGS)  # Add Safran proxy configuration

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✓ pip upgraded successfully")
            return True
        else:
            print(f"Warning: pip upgrade failed: {result.stderr}")
            print("This is often normal in corporate environments")
            return False
    except Exception as e:
        print(f"Warning: pip upgrade error: {e}")
        return False

def install_from_requirements():
    """Install packages from requirements.txt using Safran corporate proxy"""
    requirements_file = Path("requirements.txt")

    if not requirements_file.exists():
        print("Warning: requirements.txt not found")
        return False

    try:
        print("Installing packages from requirements.txt using Safran corporate repository...")
        cmd = [
            sys.executable, "-m", "pip", "install",
            "-r", str(requirements_file),
            "--quiet", "--disable-pip-version-check", "--no-warn-script-location"
        ]
        cmd.extend(SAFRAN_PIP_ARGS)  # Add Safran proxy configuration

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✓ Requirements installed successfully")
            return True
        else:
            print(f"Warning: Some requirements failed to install: {result.stderr}")
            print("This is normal in corporate environments with package restrictions")
            # Try user installation
            print("Trying user installation for requirements...")
            cmd.append("--user")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print("✓ Requirements installed to user directory")
                return True
            else:
                print(f"✗ Requirements installation failed: {result.stderr}")
                print("Some packages may not be available in the corporate repository")
                return False

    except Exception as e:
        print(f"✗ Error installing requirements: {e}")
        return False

def main():
    """Main dependency verification function for Safran corporate environment"""
    print("="*60)
    print("OCR INTELLIGENT - DEPENDENCY VERIFICATION")
    print("SAFRAN CORPORATE ENVIRONMENT")
    print("="*60)
    print(f"Using corporate proxy: {SAFRAN_INDEX_URL}")
    print(f"Trusted host: {SAFRAN_TRUSTED_HOST}")
    print("="*60)

    # Check Python version
    if not check_python_version():
        return False

    print("\nUpgrading pip...")
    upgrade_pip()

    print("\nChecking critical dependencies...")

    # Critical dependencies
    critical_deps = [
        ("streamlit", "streamlit"),
        ("psutil", "psutil"),
    ]

    all_critical_ok = True
    for package, import_name in critical_deps:
        if not check_and_install_dependency(package, import_name):
            all_critical_ok = False

    if not all_critical_ok:
        print("\n✗ Some critical dependencies failed to install")
        print("The application may not work correctly")
        print("This can be normal in corporate environments with restricted packages")

    print("\nInstalling full requirements...")
    install_from_requirements()

    print("\nFinal verification...")

    # Final check
    try:
        import streamlit
        print(f"✓ Streamlit {streamlit.__version__} is ready")
    except ImportError:
        print("✗ Streamlit is still not available")
        return False

    try:
        import psutil
        print(f"✓ psutil {psutil.__version__} is ready")
    except ImportError:
        print("Warning: psutil not available - process management will be limited")
        print("This is acceptable for basic OCR functionality")

    print("\n" + "="*60)
    print("DEPENDENCY VERIFICATION COMPLETE")
    print("SAFRAN CORPORATE ENVIRONMENT READY")
    print("="*60)
    print("You can now run the OCR Intelligent application")
    print("Note: Some warnings about package distributions are normal")

    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\nSome dependencies could not be installed.")
            print("Please check your internet connection and try again.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
