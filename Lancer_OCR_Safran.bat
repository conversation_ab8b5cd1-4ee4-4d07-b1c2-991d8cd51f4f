@echo off 
setlocal enabledelayedexpansion 
 
title OCR Intelligent - Safran Environment 
 
:: Safran Corporate Environment Configuration 
set PIP_INDEX_URL=https://artifacts.cloud.safran/repository/pypi-group/simple 
set PIP_TRUSTED_HOST=artifacts.cloud.safran 
set PIP_TIMEOUT=60 
set PIP_DISABLE_PIP_VERSION_CHECK=1 
set PYTHONIOENCODING=utf-8 
 
echo [INFO] Using Safran corporate environment configuration 
echo [INFO] Proxy: %PIP_INDEX_URL% 
echo. 
 
:: Launch the main application 
call Lancer_OCR_Intelligent.bat 
