@echo off
setlocal enabledelayedexpansion

:: Ensure the window stays open on any error
if "%1"=="DEBUG" (
    echo [DEBUG] Running in debug mode
    set DEBUG_MODE=1
) else (
    set DEBUG_MODE=0
)

title OCR Intelligent

echo ================================================================
echo OCR INTELLIGENT - Optical Character Recognition Application
echo ================================================================
echo OCR Engines: Tesseract, EasyOCR, DocTR
echo Version: Portable and Autonomous
echo Debug Mode: %DEBUG_MODE%
echo ================================================================
echo.

:: Add error handling for the entire script
goto :main

:error_handler
echo.
echo [ERROR] <PERSON><PERSON><PERSON> encountered an error at line %1
echo [ERROR] Error code: %2
echo [ERROR] Please check the error message above
echo.
echo Press any key to close this window...
pause >nul
exit /b %2

:main

:: Check Python installation with enhanced error handling
echo [STEP] Checking Python installation...
echo [DEBUG] Current working directory: %CD%
echo [DEBUG] Checking if python command is available...

python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not accessible
    echo.
    echo [DEBUG] Attempting to find Python in common locations...

    :: Try common Python installation paths
    if exist "C:\Python*\python.exe" (
        echo [INFO] Found Python installation in C:\Python*
        echo [INFO] Python may not be in PATH
    )

    if exist "%LOCALAPPDATA%\Programs\Python\*\python.exe" (
        echo [INFO] Found Python installation in %LOCALAPPDATA%\Programs\Python
        echo [INFO] Python may not be in PATH
    )

    echo.
    echo Solutions:
    echo 1. Install Python 3.8+ from https://python.org
    echo 2. Check "Add Python to PATH" during installation
    echo 3. Restart your computer after installation
    echo 4. Or run this script from a command prompt where Python is available
    echo.
    echo Current PATH (first 500 characters):
    echo %PATH:~0,500%
    echo.
    echo Press any key to close...
    pause >nul
    exit /b 1
)

echo [OK] Python detected
python --version
if errorlevel 1 (
    echo [ERROR] Python version check failed
    echo Press any key to close...
    pause >nul
    exit /b 1
)
echo.

:: Check Python version compatibility
echo [DEBUG] Getting Python version...
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
if "%PYTHON_VERSION%"=="" (
    echo [WARNING] Could not determine Python version
    set PYTHON_VERSION=Unknown
)
echo [INFO] Python version: %PYTHON_VERSION%

:: Set Safran corporate proxy configuration
echo [DEBUG] Setting up Safran corporate proxy configuration...
set PIP_INDEX_URL=https://artifacts.cloud.safran/repository/pypi-group/simple
set PIP_TRUSTED_HOST=artifacts.cloud.safran
set SAFRAN_PIP_ARGS=--index-url %PIP_INDEX_URL% --trusted-host %PIP_TRUSTED_HOST%
echo [INFO] Using Safran corporate proxy for package installation
echo [DEBUG] Proxy URL: %PIP_INDEX_URL%

:: Check and install Streamlit
echo [STEP] Checking Streamlit installation...
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing Streamlit...
    echo This may take a few minutes on first run...
    python -m pip install --upgrade pip --quiet %SAFRAN_PIP_ARGS%
    python -m pip install streamlit --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
    if errorlevel 1 (
        echo [ERROR] Failed to install Streamlit
        echo Trying alternative installation method...
        python -m pip install streamlit --user --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
        if errorlevel 1 (
            echo [ERROR] Cannot install Streamlit
            echo Please check your corporate network connection and try again
            pause
            exit /b 1
        )
    )
    echo [OK] Streamlit installed successfully
) else (
    echo [OK] Streamlit is available
)

:: Check and install psutil
echo [STEP] Checking psutil installation...
python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing psutil...
    python -m pip install psutil --quiet --no-warn-script-location %SAFRAN_PIP_ARGS%
    if errorlevel 1 (
        echo [WARNING] Failed to install psutil - process management will be limited
    ) else (
        echo [OK] psutil installed successfully
    )
) else (
    echo [OK] psutil is available
)

:: Install dependencies from requirements.txt
if exist requirements.txt (
    echo [STEP] Installing project dependencies...
    echo This may take several minutes for first-time setup...
    echo [INFO] Using Safran corporate repository...
    python -m pip install -r requirements.txt --quiet --disable-pip-version-check --no-warn-script-location %SAFRAN_PIP_ARGS%
    if errorlevel 1 (
        echo [WARNING] Some dependencies could not be installed
        echo Trying user installation...
        python -m pip install -r requirements.txt --user --quiet --disable-pip-version-check --no-warn-script-location %SAFRAN_PIP_ARGS%
        if errorlevel 1 (
            echo [WARNING] Some dependencies failed to install
            echo The application may run with limited functionality
            echo [INFO] This is normal in corporate environments with package restrictions
        ) else (
            echo [OK] Dependencies installed to user directory
        )
    ) else (
        echo [OK] All dependencies verified and installed
    )
) else (
    echo [WARNING] requirements.txt not found - some features may not work
)

:: Check for port conflicts
echo [STEP] Checking for port conflicts...
netstat -an | findstr ":8501" >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port 8501 is already in use
    echo [INFO] Application will automatically find a free port
)

:: Create necessary directories
echo [STEP] Creating working directories...
if not exist output mkdir output
if not exist logs mkdir logs
if not exist corrected mkdir corrected
echo [OK] Working directories ready

:: Final system check
echo [STEP] Final system verification...
python -c "import sys; print(f'Python executable: {sys.executable}')"
python -c "import sys; print(f'Python version: {sys.version}')"
python -c "import streamlit; print(f'Streamlit version: {streamlit.__version__}')" 2>nul
if errorlevel 1 (
    echo [ERROR] Streamlit verification failed
    pause
    exit /b 1
)

:: Launch the application
echo.
echo ================================================================
echo LAUNCHING APPLICATION
echo ================================================================
echo [INFO] Starting OCR Intelligent application...
echo [INFO] Application will automatically find a free port
echo [INFO] Browser will open automatically
echo [INFO] Press Ctrl+C to stop the application
echo.
echo If the browser doesn't open automatically, go to:
echo http://localhost:8501 (or the port shown below)
echo.

python main.py

:: Check exit code
if errorlevel 1 (
    echo.
    echo [ERROR] Application closed with an error
    echo.
    echo Possible solutions:
    echo 1. Close all Streamlit tabs in your browser
    echo 2. Restart this application
    echo 3. Check that all files are present
    echo 4. Restart your computer if the problem persists
    echo 5. Check logs in the logs/ directory
    echo.
    echo For technical support, check:
    echo - README.md for troubleshooting guide
    echo - logs/ directory for error details
    echo - requirements.txt for dependency list
    echo.
)

echo.
echo ================================================================
echo APPLICATION CLOSED
echo ================================================================
echo Thank you for using OCR Intelligent!
echo.
echo If you encountered issues:
echo 1. Check the troubleshooting section in README.md
echo 2. Verify Python and dependencies are properly installed
echo 3. Ensure you have sufficient system resources
echo 4. Run "fix_ocr_intelligent.bat" to resolve common issues
echo 5. Check logs in the logs/ directory for detailed error information
echo.

:: Final pause to ensure window stays open
echo Press any key to close this window...
pause >nul

:: End of script
goto :eof

:cleanup_and_exit
echo.
echo [INFO] Cleaning up and exiting...
echo Press any key to close...
pause >nul
exit /b 0
