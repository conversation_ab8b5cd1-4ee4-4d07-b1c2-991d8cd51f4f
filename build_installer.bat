@echo off
setlocal enabledelayedexpansion

title OCR Intelligent Installer Builder

echo ================================================================
echo OCR INTELLIGENT INSTALLER BUILDER
echo ================================================================
echo Creating single .exe installer for distribution
echo ================================================================
echo.

:: Check if Inno Setup is installed
echo [STEP] Checking Inno Setup installation...
set "INNO_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    echo [OK] Inno Setup found: C:\Program Files (x86)\Inno Setup 6\
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
    echo [OK] Inno Setup found: C:\Program Files\Inno Setup 6\
) else (
    echo [ERROR] Inno Setup is not installed
    echo.
    echo To create the installer, you need to install Inno Setup:
    echo 1. Download from: https://jrsoftware.org/isdl.php
    echo 2. Install Inno Setup 6
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

:: Check if Inno Setup script exists
if not exist "OCR_Intelligent_Setup.iss" (
    echo [ERROR] File OCR_Intelligent_Setup.iss does not exist
    echo Make sure the Inno Setup script is present
    pause
    exit /b 1
)

:: Create distribution folder
echo [STEP] Preparing build environment...
if not exist "dist" mkdir dist

:: Check required files
echo [STEP] Verifying source files...
set "MISSING_FILES="

if not exist "main.py" (
    echo [WARNING] main.py missing
    set "MISSING_FILES=1"
)

if not exist "Lancer_OCR_Intelligent.bat" (
    echo [WARNING] Lancer_OCR_Intelligent.bat missing
    set "MISSING_FILES=1"
)

if not exist "requirements.txt" (
    echo [WARNING] requirements.txt missing
    set "MISSING_FILES=1"
)

if not exist "frontend\app.py" (
    echo [WARNING] frontend\app.py missing
    set "MISSING_FILES=1"
)

if not exist "backend\main.py" (
    echo [WARNING] backend\main.py missing
    set "MISSING_FILES=1"
)

if defined MISSING_FILES (
    echo [WARNING] Some files are missing
    echo The installer will be created but may not work correctly
    echo.
    choice /C YN /M "Do you want to continue anyway"
    if errorlevel 2 exit /b 1
)

:: Build the installer
echo.
echo [STEP] Building installer...
echo This may take a few minutes...
echo.

"%INNO_PATH%" "OCR_Intelligent_Setup.iss"

if errorlevel 1 (
    echo.
    echo [ERROR] Failed to build installer
    echo Check the errors above
    pause
    exit /b 1
)

:: Check if installer was created
if exist "dist\OCR_Intelligent_Setup_v2.0.0.exe" (
    echo.
    echo ================================================================
    echo BUILD SUCCESSFUL!
    echo ================================================================
    echo Installer created successfully:
    echo File: dist\OCR_Intelligent_Setup_v2.0.0.exe
    echo.
    echo File size:
    for %%F in ("dist\OCR_Intelligent_Setup_v2.0.0.exe") do echo %%~zF bytes
    echo.
    echo This unique .exe installer contains:
    echo - All application files
    echo - Automatic Python verification
    echo - Dependency installation
    echo - Shortcut creation
    echo - Integrated uninstaller
    echo.
    echo You can now distribute this single .exe file!
    echo.
    choice /C YN /M "Do you want to open the destination folder"
    if not errorlevel 2 explorer "dist"
) else (
    echo.
    echo [ERROR] Installer not found in dist folder
    echo Check compilation errors
)

echo.
echo ================================================================
echo BUILD COMPLETED
echo ================================================================
pause
